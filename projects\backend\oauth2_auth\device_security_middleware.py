"""
Device Security Middleware
Validates device-token-IP binding and prevents authentication bypass
"""

import hashlib
from django.http import <PERSON>sonR<PERSON>ponse
from django.core.cache import cache
from django.utils import timezone
from django.contrib.auth import get_user_model
from oauth2_provider.models import AccessToken
from .utils import get_client_ip, generate_device_fingerprint, log_security_event
from agritram.logger_utils import (
    generate_unique_request_id,
    log_request_info,
    log_operation_info,
    create_logging_context,
)

User = get_user_model()


class DeviceSecurityMiddleware:
    """
    Middleware to validate device-token-IP binding and prevent security bypass
    """

    def __init__(self, get_response):
        self.get_response = get_response

        # Paths that don't require device validation
        self.exempt_paths = [
            "/user/login/",
            "/user/register/",
            "/user/activate-account/",
            "/user/forgot-password/",
            "/user/reset-password/",
            "/oauth2/",
            "/admin/",
            "/static/",
            "/media/",
            "/.well-known/",
            "/health/",
            "/docs/",
            "/swagger/",
            "/redoc/",
        ]

    def __call__(self, request):
        # Generate unique request ID for logging correlation
        unique_id = generate_unique_request_id()

        # Check if path is exempt from device validation
        if any(request.path.startswith(path) for path in self.exempt_paths):
            return self.get_response(request)

        # Only validate authenticated requests
        auth_header = request.META.get("HTTP_AUTHORIZATION", "")
        if not auth_header.startswith("Bearer "):
            return self.get_response(request)

        # Extract token
        token = auth_header.split(" ")[1] if len(auth_header.split(" ")) > 1 else None
        if not token:
            return self.get_response(request)

        # Log device security validation start
        log_operation_info(
            unique_id,
            "DEVICE_SECURITY_VALIDATION_START",
            "Starting device security validation",
            metadata={
                "client_ip": get_client_ip(request),
                "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "path": request.path,
                "method": request.method,
                "device_id": request.META.get("HTTP_X_DEVICE_ID"),
            },
        )

        # Validate device-token-IP binding
        validation_result = self.validate_device_token_binding(
            request, token, unique_id
        )

        if not validation_result["valid"]:
            log_operation_info(
                unique_id,
                "DEVICE_SECURITY_VIOLATION",
                f"Device security validation failed: {validation_result['message']}",
                metadata={
                    "client_ip": get_client_ip(request),
                    "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                    "path": request.path,
                    "validation_message": validation_result["message"],
                    "device_id": request.META.get("HTTP_X_DEVICE_ID"),
                },
                level="WARNING",
            )
            return JsonResponse(
                {
                    "error": "device_security_violation",
                    "error_description": validation_result["message"],
                    "details": "Device security validation failed",
                },
                status=403,
            )

        # Log successful validation
        log_operation_info(
            unique_id,
            "DEVICE_SECURITY_VALIDATION_SUCCESS",
            "Device security validation successful",
            metadata={
                "client_ip": get_client_ip(request),
                "device_id": validation_result["metadata"].get("device_id"),
                "security_score": validation_result["metadata"].get("security_score"),
                "validation_method": validation_result["metadata"].get(
                    "validation_method"
                ),
            },
        )

        # Add security metadata to request
        request.device_security = validation_result["metadata"]
        request.device_security["unique_id"] = (
            unique_id  # Add unique ID for downstream logging
        )

        return self.get_response(request)

    def validate_device_token_binding(self, request, token, unique_id):
        """
        Validate device-token-IP binding
        """
        try:
            # Get current request information
            current_ip = get_client_ip(request)
            current_user_agent = request.META.get("HTTP_USER_AGENT", "")
            current_fingerprint = generate_device_fingerprint(request)

            # Get device information from request headers or body
            device_id = request.META.get("HTTP_X_DEVICE_ID") or request.GET.get(
                "device_id"
            )
            client_fingerprint = request.META.get("HTTP_X_DEVICE_FINGERPRINT")
            client_security_score = request.META.get("HTTP_X_DEVICE_SECURITY_SCORE")

            # Try to get user from token
            try:
                access_token = AccessToken.objects.select_related("user").get(
                    token=token
                )
                user = access_token.user

                # Check if token is expired
                if access_token.expires < timezone.now():
                    return {"valid": False, "message": "Token expired", "metadata": {}}

            except AccessToken.DoesNotExist:
                return {"valid": False, "message": "Invalid token", "metadata": {}}

            # If device_id is not provided, try to extract from JWT payload
            if not device_id:
                try:
                    import jwt
                    from django.conf import settings

                    # Decode JWT to get device_id (without verification for metadata extraction)
                    payload = jwt.decode(token, options={"verify_signature": False})
                    device_id = payload.get("device_id")
                except Exception:
                    pass

            if not device_id:
                # Log security event for missing device ID
                log_security_event(
                    user=user,
                    event_type="missing_device_id",
                    description="Request missing device ID",
                    ip_address=current_ip,
                    user_agent=current_user_agent,
                )
                # Also log with standardized logging
                log_operation_info(
                    unique_id,
                    "DEVICE_ID_MISSING",
                    "Request missing device ID",
                    metadata={
                        "user_id": user.id,
                        "user_email": user.email,
                        "client_ip": current_ip,
                        "user_agent": current_user_agent,
                        "security_event": "missing_device_id",
                    },
                    level="WARNING",
                )
                return {
                    "valid": False,
                    "message": "Device identification required",
                    "metadata": {},
                }

            # Get stored device-token mapping
            device_token_key = f"device_token_{device_id}_{user.id}"
            stored_mapping = cache.get(device_token_key)

            if not stored_mapping:
                # Check if device is registered in database
                try:
                    device = user.device_tokens.get(device_id=device_id)
                    if not device.is_trusted:
                        return {
                            "valid": False,
                            "message": "Device not trusted",
                            "metadata": {},
                        }

                    # Device exists but no active token mapping - allow but log
                    log_security_event(
                        user=user,
                        event_type="missing_token_mapping",
                        description="Device exists but no active token mapping found",
                        ip_address=current_ip,
                        user_agent=current_user_agent,
                        device_id=device_id,
                    )
                    # Also log with standardized logging
                    log_operation_info(
                        unique_id,
                        "MISSING_TOKEN_MAPPING",
                        "Device exists but no active token mapping found",
                        metadata={
                            "user_id": user.id,
                            "user_email": user.email,
                            "client_ip": current_ip,
                            "user_agent": current_user_agent,
                            "device_id": device_id,
                            "security_event": "missing_token_mapping",
                        },
                        level="WARNING",
                    )

                    return {
                        "valid": True,
                        "message": "Device validated from database",
                        "metadata": {
                            "device_id": device_id,
                            "security_score": 60,  # Medium security
                            "validation_method": "database_fallback",
                        },
                    }

                except user.device_tokens.model.DoesNotExist:
                    log_security_event(
                        user=user,
                        event_type="unregistered_device_access",
                        description="Attempt to access with unregistered device",
                        ip_address=current_ip,
                        user_agent=current_user_agent,
                        device_id=device_id,
                    )
                    # Also log with standardized logging
                    log_operation_info(
                        unique_id,
                        "UNREGISTERED_DEVICE_ACCESS",
                        "Attempt to access with unregistered device",
                        metadata={
                            "user_id": user.id,
                            "user_email": user.email,
                            "client_ip": current_ip,
                            "user_agent": current_user_agent,
                            "device_id": device_id,
                            "security_event": "unregistered_device_access",
                        },
                        level="WARNING",
                    )
                    return {
                        "valid": False,
                        "message": "Device not registered",
                        "metadata": {},
                    }

            # Validate token hash
            current_token_hash = hashlib.sha256(token.encode()).hexdigest()
            if stored_mapping.get("token_hash") != current_token_hash:
                log_security_event(
                    user=user,
                    event_type="token_mismatch",
                    description="Token hash mismatch for device",
                    ip_address=current_ip,
                    user_agent=current_user_agent,
                    device_id=device_id,
                )
                # Also log with standardized logging
                log_operation_info(
                    unique_id,
                    "TOKEN_MISMATCH",
                    "Token hash mismatch for device",
                    metadata={
                        "user_id": user.id,
                        "user_email": user.email,
                        "client_ip": current_ip,
                        "user_agent": current_user_agent,
                        "device_id": device_id,
                        "security_event": "token_mismatch",
                    },
                    level="WARNING",
                )
                return {
                    "valid": False,
                    "message": "Token validation failed",
                    "metadata": {},
                }

            # Validate IP address (allow some flexibility for mobile networks)
            stored_ip = stored_mapping.get("ip_address")
            if stored_ip and stored_ip != current_ip:
                # Check if IPs are in same subnet (for mobile networks)
                if not self._are_ips_similar(stored_ip, current_ip):
                    log_security_event(
                        user=user,
                        event_type="ip_address_change",
                        description="Significant IP address change detected",
                        ip_address=current_ip,
                        user_agent=current_user_agent,
                        device_id=device_id,
                        metadata={"stored_ip": stored_ip, "current_ip": current_ip},
                    )
                    # Also log with standardized logging
                    log_operation_info(
                        unique_id,
                        "IP_ADDRESS_CHANGE",
                        "Significant IP address change detected",
                        metadata={
                            "user_id": user.id,
                            "user_email": user.email,
                            "client_ip": current_ip,
                            "user_agent": current_user_agent,
                            "device_id": device_id,
                            "stored_ip": stored_ip,
                            "current_ip": current_ip,
                            "security_event": "ip_address_change",
                        },
                        level="WARNING",
                    )
                    # Don't block but reduce security score
                    security_score = max(
                        stored_mapping.get("security_score", 50) - 20, 30
                    )
                else:
                    security_score = stored_mapping.get("security_score", 50)
            else:
                security_score = stored_mapping.get("security_score", 50)

            # Validate user agent (allow some flexibility)
            stored_ua_hash = stored_mapping.get("user_agent_hash")
            current_ua_hash = hashlib.sha256(current_user_agent.encode()).hexdigest()[
                :16
            ]

            if stored_ua_hash and stored_ua_hash != current_ua_hash:
                log_security_event(
                    user=user,
                    event_type="user_agent_change",
                    description="User agent change detected",
                    ip_address=current_ip,
                    user_agent=current_user_agent,
                    device_id=device_id,
                )
                # Also log with standardized logging
                log_operation_info(
                    unique_id,
                    "USER_AGENT_CHANGE",
                    "User agent change detected",
                    metadata={
                        "user_id": user.id,
                        "user_email": user.email,
                        "client_ip": current_ip,
                        "user_agent": current_user_agent,
                        "device_id": device_id,
                        "security_event": "user_agent_change",
                    },
                    level="WARNING",
                )
                security_score = max(security_score - 10, 20)

            # Validate device fingerprint if available
            stored_fingerprint = stored_mapping.get("device_fingerprint")
            fingerprint_to_check = client_fingerprint or current_fingerprint

            if stored_fingerprint and fingerprint_to_check:
                from .utils import calculate_fingerprint_similarity

                similarity = calculate_fingerprint_similarity(
                    stored_fingerprint, fingerprint_to_check
                )

                if similarity < 0.7:  # Less than 70% similarity
                    log_security_event(
                        user=user,
                        event_type="device_fingerprint_mismatch",
                        description="Device fingerprint mismatch detected",
                        ip_address=current_ip,
                        user_agent=current_user_agent,
                        device_id=device_id,
                        metadata={
                            "similarity_score": similarity,
                            "stored_fingerprint": stored_fingerprint[:20]
                            + "...",  # Truncated for security
                            "current_fingerprint": fingerprint_to_check[:20] + "...",
                        },
                    )
                    # Also log with standardized logging
                    log_operation_info(
                        unique_id,
                        "DEVICE_FINGERPRINT_MISMATCH",
                        "Device fingerprint mismatch detected",
                        metadata={
                            "user_id": user.id,
                            "user_email": user.email,
                            "client_ip": current_ip,
                            "user_agent": current_user_agent,
                            "device_id": device_id,
                            "similarity_score": similarity,
                            "stored_fingerprint_preview": stored_fingerprint[:20]
                            + "...",
                            "current_fingerprint_preview": fingerprint_to_check[:20]
                            + "...",
                            "security_event": "device_fingerprint_mismatch",
                        },
                        level="WARNING",
                    )
                    security_score = max(security_score - 15, 10)

            # Validate client-provided security score
            if client_security_score:
                try:
                    client_score = int(client_security_score)
                    if client_score < 50:  # Low client security score
                        security_score = max(security_score - 10, 20)
                except ValueError:
                    pass

            # Check minimum security score
            if security_score < 30:
                return {
                    "valid": False,
                    "message": "Device security score too low",
                    "metadata": {"security_score": security_score},
                }

            return {
                "valid": True,
                "message": "Device validation successful",
                "metadata": {
                    "device_id": device_id,
                    "security_score": security_score,
                    "validation_method": "full_validation",
                },
            }

        except Exception as e:
            # Also log with standardized logging
            log_operation_info(
                unique_id,
                "DEVICE_VALIDATION_ERROR",
                f"Device security validation error: {str(e)}",
                metadata={
                    "client_ip": get_client_ip(request),
                    "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                    "device_id": request.META.get("HTTP_X_DEVICE_ID"),
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return {
                "valid": False,
                "message": "Device validation error",
                "metadata": {},
            }

    def _are_ips_similar(self, ip1, ip2):
        """
        Check if two IP addresses are similar (same subnet)
        """
        try:
            import ipaddress

            # Convert to IP objects
            addr1 = ipaddress.ip_address(ip1)
            addr2 = ipaddress.ip_address(ip2)

            # Check if both are IPv4 or IPv6
            if type(addr1) != type(addr2):
                return False

            # For IPv4, check if in same /24 subnet
            if isinstance(addr1, ipaddress.IPv4Address):
                network1 = ipaddress.ip_network(f"{ip1}/24", strict=False)
                return addr2 in network1

            # For IPv6, check if in same /64 subnet
            if isinstance(addr1, ipaddress.IPv6Address):
                network1 = ipaddress.ip_network(f"{ip1}/64", strict=False)
                return addr2 in network1

            return False
        except Exception:
            return False
