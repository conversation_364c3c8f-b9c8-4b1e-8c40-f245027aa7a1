"""
Django management command for security auditing and monitoring
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from datetime import timedelta
from django.db import models
from oauth2_auth.security_logger import security_logger
from oauth2_auth.models import SecurityE<PERSON>, DeviceToken
from oauth2_provider.models import AccessToken, RefreshToken, Application
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
)


class Command(BaseCommand):
    help = "Perform security audit and generate security reports"

    def add_arguments(self, parser):
        parser.add_argument(
            "--days", type=int, default=7, help="Number of days to analyze (default: 7)"
        )
        parser.add_argument(
            "--format",
            type=str,
            choices=["table", "json", "csv"],
            default="table",
            help="Output format (default: table)",
        )
        parser.add_argument(
            "--output-file", type=str, help="Output file path (optional)"
        )
        parser.add_argument(
            "--alert-threshold",
            type=int,
            default=10,
            help="Alert threshold for suspicious activities (default: 10)",
        )

    def handle(self, *args, **options):
        days = options["days"]
        output_format = options["format"]
        output_file = options["output_file"]
        alert_threshold = options["alert_threshold"]

        self.stdout.write(f"Starting security audit for the last {days} days...")

        try:
            # Get security summary
            summary = security_logger.get_security_summary(days=days)

            if "error" in summary:
                raise CommandError(
                    f'Failed to get security summary: {summary["error"]}'
                )

            # Perform detailed analysis
            analysis = self._perform_detailed_analysis(days, alert_threshold)

            # Generate report
            report = {
                "audit_date": timezone.now().isoformat(),
                "period_days": days,
                "summary": summary,
                "analysis": analysis,
                "recommendations": self._generate_recommendations(summary, analysis),
            }

            # Output report
            if output_format == "json":
                self._output_json(report, output_file)
            elif output_format == "csv":
                self._output_csv(report, output_file)
            else:
                self._output_table(report, output_file)

            # Check for alerts
            self._check_security_alerts(analysis, alert_threshold)

            self.stdout.write(
                self.style.SUCCESS("Security audit completed successfully!")
            )

        except Exception as e:
            unique_id = generate_unique_request_id()
            log_operation_info(
                unique_id,
                "SECURITY_AUDIT_ERROR",
                f"Security audit failed: {str(e)}",
                metadata={
                    "command_options": {
                        "days": options.get("days"),
                        "alert_threshold": options.get("alert_threshold"),
                        "detailed": options.get("detailed"),
                        "export": options.get("export"),
                    },
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            raise CommandError(f"Security audit failed: {str(e)}")

    def _perform_detailed_analysis(self, days, alert_threshold):
        """Perform detailed security analysis"""
        cutoff_date = timezone.now() - timedelta(days=days)

        analysis = {
            "suspicious_ips": [],
            "blocked_devices": 0,
            "failed_login_patterns": {},
            "token_anomalies": {},
            "device_registrations": 0,
            "high_risk_users": [],
            "security_alerts_sent": 0,
        }

        try:
            # Analyze suspicious IPs
            failed_logins = SecurityEvent.objects.filter(
                event_type="failed_login", created_at__gte=cutoff_date
            )

            ip_failures = {}
            for event in failed_logins:
                if event.ip_address:
                    ip_failures[event.ip_address] = (
                        ip_failures.get(event.ip_address, 0) + 1
                    )

            analysis["suspicious_ips"] = [
                {"ip": ip, "failures": count}
                for ip, count in ip_failures.items()
                if count >= alert_threshold
            ]

            # Analyze blocked devices
            analysis["blocked_devices"] = DeviceToken.objects.filter(
                is_blocked=True, blocked_at__gte=cutoff_date
            ).count()

            # Analyze failed login patterns
            for event in failed_logins:
                hour = event.created_at.hour
                analysis["failed_login_patterns"][hour] = (
                    analysis["failed_login_patterns"].get(hour, 0) + 1
                )

            # Analyze token anomalies
            token_events = SecurityEvent.objects.filter(
                event_type__in=["invalid_token_usage", "token_ip_change"],
                created_at__gte=cutoff_date,
            )

            for event in token_events:
                event_type = event.event_type
                analysis["token_anomalies"][event_type] = (
                    analysis["token_anomalies"].get(event_type, 0) + 1
                )

            # Count device registrations
            analysis["device_registrations"] = SecurityEvent.objects.filter(
                event_type="device_registered", created_at__gte=cutoff_date
            ).count()

            # Identify high-risk users
            high_risk_events = (
                SecurityEvent.objects.filter(
                    event_type__in=[
                        "failed_login",
                        "suspicious_activity",
                        "device_blocked",
                    ],
                    created_at__gte=cutoff_date,
                )
                .values("user__email")
                .annotate(event_count=models.Count("id"))
                .filter(event_count__gte=alert_threshold)
            )

            analysis["high_risk_users"] = [
                {"email": item["user__email"], "events": item["event_count"]}
                for item in high_risk_events
            ]

            # Count security alerts
            analysis["security_alerts_sent"] = SecurityEvent.objects.filter(
                event_type="security_alert_sent", created_at__gte=cutoff_date
            ).count()

        except Exception as e:
            unique_id = generate_unique_request_id()
            log_operation_info(
                unique_id,
                "DETAILED_SECURITY_ANALYSIS_ERROR",
                f"Detailed analysis failed: {str(e)}",
                metadata={
                    "days": days,
                    "alert_threshold": alert_threshold,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

        return analysis

    def _generate_recommendations(self, summary, analysis):
        """Generate security recommendations"""
        recommendations = []

        # High failed login rate
        if summary.get("failed_logins", 0) > 100:
            recommendations.append(
                {
                    "priority": "HIGH",
                    "category": "Authentication",
                    "issue": f"High number of failed logins: {summary['failed_logins']}",
                    "recommendation": "Consider implementing stricter rate limiting and account lockout policies",
                }
            )

        # Suspicious IPs
        if len(analysis["suspicious_ips"]) > 0:
            recommendations.append(
                {
                    "priority": "HIGH",
                    "category": "Network Security",
                    "issue": f"Suspicious IPs detected: {len(analysis['suspicious_ips'])}",
                    "recommendation": "Review and consider blocking suspicious IP addresses",
                }
            )

        # High-risk users
        if len(analysis["high_risk_users"]) > 0:
            recommendations.append(
                {
                    "priority": "MEDIUM",
                    "category": "User Security",
                    "issue": f"High-risk users identified: {len(analysis['high_risk_users'])}",
                    "recommendation": "Contact high-risk users and review their account security",
                }
            )

        # Token anomalies
        if analysis["token_anomalies"]:
            recommendations.append(
                {
                    "priority": "MEDIUM",
                    "category": "Token Security",
                    "issue": f"Token anomalies detected: {sum(analysis['token_anomalies'].values())}",
                    "recommendation": "Review token usage patterns and consider token rotation",
                }
            )

        # No recent activity
        if summary.get("total_events", 0) == 0:
            recommendations.append(
                {
                    "priority": "LOW",
                    "category": "Monitoring",
                    "issue": "No security events recorded",
                    "recommendation": "Verify security logging is working correctly",
                }
            )

        return recommendations

    def _output_table(self, report, output_file):
        """Output report in table format"""
        output = []

        output.append("=" * 80)
        output.append("SECURITY AUDIT REPORT")
        output.append("=" * 80)
        output.append(f"Audit Date: {report['audit_date']}")
        output.append(f"Period: {report['period_days']} days")
        output.append("")

        # Summary
        output.append("SUMMARY:")
        output.append("-" * 40)
        summary = report["summary"]
        output.append(f"Total Events: {summary.get('total_events', 0)}")
        output.append(f"Unique Users: {summary.get('unique_users', 0)}")
        output.append(f"Unique IPs: {summary.get('unique_ips', 0)}")
        output.append(f"High Risk Events: {summary.get('high_risk_events', 0)}")
        output.append(f"Critical Events: {summary.get('critical_events', 0)}")
        output.append(f"Failed Logins: {summary.get('failed_logins', 0)}")
        output.append(f"Successful Logins: {summary.get('successful_logins', 0)}")
        output.append("")

        # Analysis
        output.append("DETAILED ANALYSIS:")
        output.append("-" * 40)
        analysis = report["analysis"]
        output.append(f"Suspicious IPs: {len(analysis['suspicious_ips'])}")
        output.append(f"Blocked Devices: {analysis['blocked_devices']}")
        output.append(f"Device Registrations: {analysis['device_registrations']}")
        output.append(f"High Risk Users: {len(analysis['high_risk_users'])}")
        output.append(f"Security Alerts Sent: {analysis['security_alerts_sent']}")
        output.append("")

        # Recommendations
        output.append("RECOMMENDATIONS:")
        output.append("-" * 40)
        for rec in report["recommendations"]:
            output.append(f"[{rec['priority']}] {rec['category']}: {rec['issue']}")
            output.append(f"    → {rec['recommendation']}")
            output.append("")

        output.append("=" * 80)

        report_text = "\n".join(output)

        if output_file:
            with open(output_file, "w") as f:
                f.write(report_text)
            self.stdout.write(f"Report saved to: {output_file}")
        else:
            self.stdout.write(report_text)

    def _output_json(self, report, output_file):
        """Output report in JSON format"""
        import json

        json_output = json.dumps(report, indent=2, default=str)

        if output_file:
            with open(output_file, "w") as f:
                f.write(json_output)
            self.stdout.write(f"JSON report saved to: {output_file}")
        else:
            self.stdout.write(json_output)

    def _output_csv(self, report, output_file):
        """Output report in CSV format"""
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # Write summary data
        writer.writerow(["Metric", "Value"])
        summary = report["summary"]
        for key, value in summary.items():
            if key != "event_types":
                writer.writerow([key.replace("_", " ").title(), value])

        csv_output = output.getvalue()

        if output_file:
            with open(output_file, "w") as f:
                f.write(csv_output)
            self.stdout.write(f"CSV report saved to: {output_file}")
        else:
            self.stdout.write(csv_output)

    def _check_security_alerts(self, analysis, alert_threshold):
        """Check for security alerts that need immediate attention"""
        alerts = []

        if len(analysis["suspicious_ips"]) > 0:
            alerts.append(
                f"ALERT: {len(analysis['suspicious_ips'])} suspicious IPs detected"
            )

        if analysis["blocked_devices"] > alert_threshold:
            alerts.append(f"ALERT: {analysis['blocked_devices']} devices blocked")

        if len(analysis["high_risk_users"]) > 5:
            alerts.append(
                f"ALERT: {len(analysis['high_risk_users'])} high-risk users identified"
            )

        if alerts:
            self.stdout.write(self.style.ERROR("\nSECURITY ALERTS:"))
            for alert in alerts:
                self.stdout.write(self.style.ERROR(f"  ⚠️  {alert}"))
        else:
            self.stdout.write(
                self.style.SUCCESS("\n✅ No critical security alerts detected")
            )
