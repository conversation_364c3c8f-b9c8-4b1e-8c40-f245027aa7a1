#!/usr/bin/env python
"""
Test script to verify the consolidated UUID generation functions work correctly.
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
django.setup()

from agritram.logger_utils import (
    generate_unique_id,
    generate_api_call_uuid,
    generate_unique_request_id,
    enhanced_log,
    LoggingContext,
)


def test_uuid_functions():
    """Test all UUID generation functions."""
    print("🧪 Testing UUID Generation Functions")
    print("=" * 50)
    
    # Test main function
    id1 = generate_unique_id()
    print(f"generate_unique_id():        {id1}")
    
    # Test backward compatibility aliases
    id2 = generate_api_call_uuid()
    print(f"generate_api_call_uuid():    {id2}")
    
    id3 = generate_unique_request_id()
    print(f"generate_unique_request_id(): {id3}")
    
    # Verify format
    assert id1.startswith("API_"), f"Expected API_ prefix, got: {id1}"
    assert id2.startswith("API_"), f"Expected API_ prefix, got: {id2}"
    assert id3.startswith("API_"), f"Expected API_ prefix, got: {id3}"
    
    # Verify uniqueness
    assert id1 != id2 != id3, "UUIDs should be unique"
    
    # Verify length (API_ + 36 char UUID = 40 chars)
    assert len(id1) == 40, f"Expected 40 chars, got {len(id1)}"
    assert len(id2) == 40, f"Expected 40 chars, got {len(id2)}"
    assert len(id3) == 40, f"Expected 40 chars, got {len(id3)}"
    
    print("\n✅ All UUID functions work correctly!")
    print("✅ All functions use the same format: API_<uuid>")
    print("✅ All UUIDs are unique")
    print("✅ Backward compatibility maintained")


def test_logging_with_consolidated_uuid():
    """Test that logging works with the consolidated UUID function."""
    print("\n🧪 Testing Logging with Consolidated UUID")
    print("=" * 50)
    
    # Test direct logging
    enhanced_log(
        message="Testing consolidated UUID in logging",
        operation_type="UUID_TEST"
    )
    
    # Test with context
    with LoggingContext(operation="uuid_consolidation_test", test_id=12345):
        enhanced_log(
            message="Testing within LoggingContext",
            operation_type="CONTEXT_UUID_TEST"
        )
        
        # Test nested function call
        nested_function_test()
    
    print("✅ Logging works correctly with consolidated UUID!")


def nested_function_test():
    """Test that nested functions inherit the UUID correctly."""
    enhanced_log(
        message="This is from a nested function",
        operation_type="NESTED_FUNCTION_TEST",
        metadata={"nested": True, "level": 2}
    )


def test_uuid_format_consistency():
    """Test that all UUIDs follow the same format."""
    print("\n🧪 Testing UUID Format Consistency")
    print("=" * 50)
    
    # Generate multiple UUIDs
    uuids = []
    for i in range(5):
        uuids.append(generate_unique_id())
    
    print("Generated UUIDs:")
    for i, uuid_val in enumerate(uuids, 1):
        print(f"  {i}. {uuid_val}")
    
    # Verify all have same format
    for uuid_val in uuids:
        assert uuid_val.startswith("API_"), f"UUID should start with API_: {uuid_val}"
        assert len(uuid_val) == 40, f"UUID should be 40 chars: {uuid_val} ({len(uuid_val)})"
        assert uuid_val.count("-") == 4, f"UUID should have 4 hyphens: {uuid_val}"
    
    # Verify all are unique
    assert len(set(uuids)) == len(uuids), "All UUIDs should be unique"
    
    print("✅ All UUIDs follow consistent format!")
    print("✅ All UUIDs are unique!")


def main():
    """Run all UUID consolidation tests."""
    print("🚀 UUID Consolidation Testing")
    print("=" * 60)
    
    try:
        test_uuid_functions()
        test_logging_with_consolidated_uuid()
        test_uuid_format_consistency()
        
        print("\n" + "=" * 60)
        print("🎉 ALL UUID CONSOLIDATION TESTS PASSED!")
        print("\n📋 Summary:")
        print("   ✅ Single generate_unique_id() function works")
        print("   ✅ Backward compatibility aliases work")
        print("   ✅ All UUIDs use consistent API_<uuid> format")
        print("   ✅ Logging integration works correctly")
        print("   ✅ Thread-local storage uses consolidated function")
        print("\n💡 Recommendation:")
        print("   • Use generate_unique_id() for new code")
        print("   • Old functions still work for backward compatibility")
        print("   • All functions now produce the same format")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
