from django.utils import timezone
from rest_framework.authentication import (
    BaseAuthentication,
    SessionAuthentication,
    TokenAuthentication,
)
from rest_framework.exceptions import AuthenticationFailed
from oauth2_provider.models import AccessToken
from .models import DeviceToken
from .utils import log_security_event
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
    log_database_operation,
    log_security_event_standardized,
    log_business_event,
    log_performance_metric,
    create_logging_context,
    get_client_ip,
)


def _get_user_status_validator():
    """Lazy import to avoid circular imports"""
    from user.auth_utils import UserStatusValidator

    return UserStatusValidator


def _log_authentication_attempt(*args, **kwargs):
    """Lazy import to avoid circular imports"""
    from user.auth_utils import log_authentication_attempt

    return log_authentication_attempt(*args, **kwargs)


class OAuth2Authentication(BaseAuthentication):
    """
    Custom OAuth2 authentication class with enhanced security
    """

    def authenticate(self, request):
        """
        Authenticate using OAuth2 Bearer token
        """
        import time

        start_time = time.time()

        # Generate unique request ID for logging correlation
        unique_id = generate_unique_request_id()

        # Create logging context
        logging_context = create_logging_context(
            unique_id=unique_id, request=request, operation_name="oauth2_authentication"
        )

        auth_header = request.META.get("HTTP_AUTHORIZATION", "")

        if not auth_header.startswith("Bearer "):
            log_operation_info(
                unique_id,
                "OAUTH2_AUTHENTICATION_SKIPPED",
                "No Bearer token found in Authorization header",
                metadata={
                    "client_ip": get_client_ip(request),
                    "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                    "path": request.path,
                    "method": request.method,
                    "auth_header_present": bool(auth_header),
                    "auth_header_type": (
                        auth_header.split(" ")[0] if auth_header else "none"
                    ),
                },
            )
            return None

        token = auth_header.split(" ")[1] if len(auth_header.split(" ")) > 1 else None
        if not token:
            log_operation_info(
                unique_id,
                "OAUTH2_AUTHENTICATION_ERROR",
                "Empty Bearer token provided",
                metadata={
                    "client_ip": get_client_ip(request),
                    "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                    "path": request.path,
                    "method": request.method,
                    "error": "empty_token",
                },
                level="WARNING",
            )
            return None

        try:
            # Log authentication attempt start
            log_operation_info(
                unique_id,
                "OAUTH2_AUTHENTICATION_START",
                "Starting OAuth2 token authentication",
                metadata={
                    "client_ip": get_client_ip(request),
                    "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                    "path": request.path,
                    "method": request.method,
                    "token_preview": token[:10] + "..." if len(token) > 10 else token,
                    "operation_type": "oauth2_authentication",
                },
            )
            # First check if token is blacklisted using the new token invalidation service
            from .token_invalidation_service import token_invalidation_service

            if token_invalidation_service.is_token_blacklisted(token):
                # Log security event with standardized logging
                log_security_event_standardized(
                    unique_id,
                    "BLACKLISTED_TOKEN_USAGE",
                    "Attempt to use blacklisted token",
                    request=request,
                    metadata={
                        "token_preview": token[:10] + "...",
                        "security_violation": "blacklisted_token_usage",
                        "threat_level": "medium",
                    },
                    level="WARNING",
                )

                # Log business event for security monitoring
                log_business_event(
                    unique_id,
                    "SECURITY_VIOLATION",
                    "Blacklisted token usage attempt detected",
                    entity_type="SECURITY_EVENT",
                    entity_id=unique_id,
                    metadata={
                        "violation_type": "blacklisted_token_usage",
                        "token_preview": token[:10] + "...",
                        "client_ip": get_client_ip(request),
                        "path": request.path,
                    },
                    level="WARNING",
                )

                # Log with traditional security event for backward compatibility
                log_security_event(
                    event_type="blacklisted_token_usage",
                    description="Attempt to use blacklisted token",
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    metadata={"token": token[:10] + "..."},
                )

                # Also log with standardized logging
                log_operation_info(
                    unique_id,
                    "BLACKLISTED_TOKEN_USAGE",
                    "Attempt to use blacklisted token",
                    metadata={
                        "client_ip": get_client_ip(request),
                        "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                        "path": request.path,
                        "token_preview": token[:10] + "...",
                        "security_event": "blacklisted_token_usage",
                        "authentication_result": "failed",
                    },
                    level="WARNING",
                )
                raise AuthenticationFailed("Token has been invalidated")

            # Log database operation for token lookup
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_accesstoken",
                metadata={
                    "token_preview": token[:10] + "...",
                    "query_type": "get_by_token_with_relations",
                    "relations": ["user", "application"],
                },
            )

            # Try to find the access token
            access_token = AccessToken.objects.select_related(
                "user", "application"
            ).get(token=token)

            # Log successful database operation
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_accesstoken",
                operation_result="SUCCESS",
                metadata={
                    "token_preview": token[:10] + "...",
                    "access_token_id": access_token.id,
                    "user_id": access_token.user.id,
                    "application_id": (
                        access_token.application.id
                        if access_token.application
                        else None
                    ),
                },
            )

            # Check if token is valid
            if not access_token.is_valid():
                # Log security event with standardized logging
                log_security_event_standardized(
                    unique_id,
                    "INVALID_TOKEN_USAGE",
                    "Attempt to use invalid/expired token",
                    request=request,
                    metadata={
                        "token_preview": token[:10] + "...",
                        "access_token_id": access_token.id,
                        "token_expired": access_token.expires < timezone.now(),
                        "token_expires": (
                            access_token.expires.isoformat()
                            if access_token.expires
                            else None
                        ),
                        "security_violation": "invalid_token_usage",
                    },
                    level="WARNING",
                )

                # Log traditional security event for backward compatibility
                log_security_event(
                    event_type="invalid_token_usage",
                    description="Attempt to use invalid/expired token",
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    metadata={"token": token[:10] + "..."},
                )

                log_operation_info(
                    unique_id,
                    "INVALID_TOKEN_USAGE",
                    "Token validation failed - invalid or expired",
                    metadata={
                        "token_preview": token[:10] + "...",
                        "access_token_id": access_token.id,
                        "token_expired": access_token.expires < timezone.now(),
                        "authentication_result": "failed",
                        "failure_reason": "invalid_or_expired_token",
                    },
                    level="WARNING",
                )
                raise AuthenticationFailed("Token is invalid or expired")

            # Validate user status before allowing authentication
            user = access_token.user
            UserStatusValidator = _get_user_status_validator()
            validation_result = UserStatusValidator.validate_user_status(
                user, log_violations=True
            )

            if not validation_result["is_valid"]:
                # Log authentication attempt with user status violation
                _log_authentication_attempt(
                    user=user,
                    success=False,
                    method="oauth2_token",
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    error_code=validation_result["error_code"],
                )

                # Raise authentication failed with specific error
                raise AuthenticationFailed(
                    f"{validation_result['error_message']}: {validation_result['error_details']}"
                )

            # Log successful authentication with comprehensive logging
            auth_time = time.time() - start_time

            # Log performance metric
            log_performance_metric(
                unique_id,
                "oauth2_authentication_time",
                auth_time * 1000,  # Convert to milliseconds
                unit="ms",
                operation_type="oauth2_authentication",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "application_id": (
                        access_token.application.id
                        if access_token.application
                        else None
                    ),
                    "token_id": access_token.id,
                },
            )

            # Log business event for successful authentication
            log_business_event(
                unique_id,
                "USER_AUTHENTICATED",
                f"User successfully authenticated via OAuth2 token: {user.email}",
                entity_type="USER",
                entity_id=str(user.id),
                metadata={
                    "authentication_method": "oauth2_token",
                    "user_email": user.email,
                    "application_id": (
                        access_token.application.id
                        if access_token.application
                        else None
                    ),
                    "token_id": access_token.id,
                    "authentication_time_ms": auth_time * 1000,
                },
            )

            # Log successful authentication with traditional method
            _log_authentication_attempt(
                user=user,
                success=True,
                method="oauth2_token",
                ip_address=get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
            )

            # Log comprehensive operation success
            log_operation_info(
                unique_id,
                "OAUTH2_AUTHENTICATION_SUCCESS",
                f"OAuth2 authentication successful for user: {user.email}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "application_id": (
                        access_token.application.id
                        if access_token.application
                        else None
                    ),
                    "token_id": access_token.id,
                    "authentication_method": "oauth2_token",
                    "authentication_time_ms": auth_time * 1000,
                    "authentication_result": "success",
                },
            )

            return (access_token.user, access_token)

        except AccessToken.DoesNotExist:
            # Log database operation failure
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_accesstoken",
                operation_result="NOT_FOUND",
                metadata={
                    "token_preview": token[:10] + "...",
                    "error": "AccessToken does not exist",
                },
                level="WARNING",
            )

            log_operation_info(
                unique_id,
                "OAUTH2_TOKEN_NOT_FOUND",
                "OAuth2 access token not found, attempting JWT validation",
                metadata={
                    "token_preview": token[:10] + "...",
                    "fallback_method": "jwt_validation",
                    "authentication_step": "jwt_fallback",
                },
            )

            # Try JWT token validation using the new JWT rotation service
            from .jwt_rotation_service import jwt_rotation_service

            is_valid, jwt_payload = jwt_rotation_service.validate_access_token(token)

            if is_valid and jwt_payload:
                log_operation_info(
                    unique_id,
                    "JWT_TOKEN_VALIDATION_SUCCESS",
                    "JWT token validation successful",
                    metadata={
                        "token_preview": token[:10] + "...",
                        "jwt_subject": jwt_payload.get("sub"),
                        "jwt_issuer": jwt_payload.get("iss"),
                        "jwt_expires": jwt_payload.get("exp"),
                        "authentication_method": "jwt_token",
                    },
                )
                try:
                    from django.contrib.auth import get_user_model

                    User = get_user_model()
                    user = User.objects.get(id=jwt_payload["sub"])

                    # Validate user status for JWT authentication
                    UserStatusValidator = _get_user_status_validator()
                    validation_result = UserStatusValidator.validate_user_status(
                        user, log_violations=True
                    )

                    if not validation_result["is_valid"]:
                        # Log authentication attempt with user status violation
                        _log_authentication_attempt(
                            user=user,
                            success=False,
                            method="jwt_token",
                            ip_address=get_client_ip(request),
                            user_agent=request.META.get("HTTP_USER_AGENT", ""),
                            error_code=validation_result["error_code"],
                        )

                        # Raise authentication failed with specific error
                        raise AuthenticationFailed(
                            f"{validation_result['error_message']}: {validation_result['error_details']}"
                        )

                    # Additional validation: Check if JWT token status matches current user status
                    if "user_status" in jwt_payload:
                        token_status = jwt_payload["user_status"]
                        current_status = UserStatusValidator.get_user_status_summary(
                            user
                        )

                        # Check for critical status changes since token was issued
                        critical_changes = []
                        if (
                            token_status.get("is_active", True)
                            and not current_status["is_active"]
                        ):
                            critical_changes.append("account_deactivated")
                        if (
                            not token_status.get("is_deleted", False)
                            and current_status["is_deleted"]
                        ):
                            critical_changes.append("account_deleted")
                        if (
                            not token_status.get("is_locked", False)
                            and current_status["is_locked"]
                        ):
                            critical_changes.append("account_locked")

                        if critical_changes:
                            raise AuthenticationFailed(
                                "Token is no longer valid due to account status changes. Please login again."
                            )

                    # Log successful JWT authentication
                    _log_authentication_attempt(
                        user=user,
                        success=True,
                        method="jwt_token",
                        ip_address=get_client_ip(request),
                        user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    )

                    return (user, jwt_payload)
                except User.DoesNotExist:
                    pass

            log_security_event(
                event_type="invalid_token_usage",
                description="Attempt to use non-existent token",
                ip_address=get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                metadata={"token": token[:10] + "..."},
            )
            raise AuthenticationFailed("Invalid token")

        except Exception as e:
            raise AuthenticationFailed("Authentication failed")


class DeviceAuthenticationService:
    """
    Service for device-based authentication and trust management
    """

    @staticmethod
    def validate_device_for_registration(
        device_id, device_type, request, unique_id=None
    ):
        """
        Validate device consistency for registration

        Args:
            device_id: Device identifier
            device_type: Device type to validate
            request: HTTP request object
            unique_id: Unique request ID for correlation

        Returns:
            dict: Validation result with is_valid, message, and details
        """
        from .models import DeviceToken
        from .utils import get_client_ip, generate_device_fingerprint

        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        validation_result = {
            "is_valid": True,
            "message": "Device validation passed",
            "details": {},
            "warnings": [],
        }

        try:
            existing_device = DeviceToken.objects.get(device_id=device_id)

            # Device exists - perform consistency checks
            current_ip = get_client_ip(request)
            current_fingerprint = generate_device_fingerprint(request)

            # Check device type consistency
            if existing_device.device_type != device_type:
                validation_result["is_valid"] = False
                validation_result["message"] = "Device type mismatch"
                validation_result["details"]["device_type_mismatch"] = {
                    "stored": existing_device.device_type,
                    "provided": device_type,
                }

            # Check IP consistency (warning only)
            if existing_device.ip_address != current_ip:
                validation_result["warnings"].append("ip_address_changed")
                validation_result["details"]["ip_change"] = {
                    "stored": existing_device.ip_address,
                    "current": current_ip,
                }

            # Check fingerprint consistency (warning only)
            if existing_device.fingerprint != current_fingerprint:
                validation_result["warnings"].append("device_fingerprint_changed")
                validation_result["details"]["fingerprint_change"] = {
                    "stored": existing_device.fingerprint,
                    "current": current_fingerprint,
                }

            validation_result["details"]["existing_device"] = {
                "user_email": existing_device.user.email,
                "device_name": existing_device.device_name,
                "last_seen": (
                    existing_device.last_seen.isoformat()
                    if existing_device.last_seen
                    else None
                ),
                "is_trusted": existing_device.is_trusted,
            }

        except DeviceToken.DoesNotExist:
            # New device - this is fine for registration
            validation_result["details"]["is_new_device"] = True

        # Log validation result
        log_operation_info(
            unique_id,
            "DEVICE_REGISTRATION_VALIDATION",
            f"Device validation for registration: {validation_result['message']}",
            metadata={
                "device_id": device_id,
                "device_type": device_type,
                "validation_passed": validation_result["is_valid"],
                "warnings": validation_result["warnings"],
                "details": validation_result["details"],
            },
            level="WARNING" if not validation_result["is_valid"] else "INFO",
        )

        return validation_result

    @staticmethod
    def register_device(
        user, device_id, device_name, device_type, request, unique_id=None
    ):
        """
        Register a new device for the user
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "DEVICE_REGISTRATION_START",
            f"Starting device registration for user: {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "device_id": device_id,
                "device_name": device_name,
                "device_type": device_type,
                "operation_type": "device_registration",
            },
        )

        try:
            from .utils import generate_device_fingerprint

            fingerprint = generate_device_fingerprint(request)
            ip_address = get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")

            # Log database operation
            log_database_operation(
                unique_id,
                "CREATE",
                "oauth2_auth_devicetoken",
                metadata={
                    "user_id": user.id,
                    "device_id": device_id,
                    "device_name": device_name,
                    "device_type": device_type,
                    "operation": "get_or_create",
                },
            )

            device, created = DeviceToken.objects.get_or_create(
                user=user,
                device_id=device_id,
                defaults={
                    "device_name": device_name,
                    "device_type": device_type,
                    "fingerprint": fingerprint,
                    "ip_address": ip_address,
                    "user_agent": user_agent,
                },
            )

            if not created:
                # Update existing device
                log_database_operation(
                    unique_id,
                    "UPDATE",
                    "oauth2_auth_devicetoken",
                    metadata={
                        "device_id": device_id,
                        "device_pk": device.pk,
                        "operation": "update_existing_device",
                    },
                )

                device.device_name = device_name
                device.device_type = device_type
                device.fingerprint = fingerprint
                device.ip_address = ip_address
                device.user_agent = user_agent
                device.last_seen = timezone.now()
                device.save()

            # Log successful database operation
            log_database_operation(
                unique_id,
                "CREATE" if created else "UPDATE",
                "oauth2_auth_devicetoken",
                operation_result="SUCCESS",
                metadata={
                    "device_id": device_id,
                    "device_pk": device.pk,
                    "created": created,
                    "user_id": user.id,
                },
            )

            # Log security event with standardized logging
            log_security_event_standardized(
                unique_id,
                "DEVICE_REGISTERED",
                f"Device registered: {device_name}",
                user=user,
                request=request,
                metadata={
                    "device_id": device_id,
                    "device_name": device_name,
                    "device_type": device_type,
                    "fingerprint": fingerprint,
                    "created": created,
                    "device_pk": device.pk,
                },
            )

            # Log business event
            log_business_event(
                unique_id,
                "DEVICE_REGISTERED",
                f"New device registered for user: {user.email}",
                entity_type="DEVICE",
                entity_id=str(device.pk),
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device_name,
                    "device_type": device_type,
                    "created": created,
                },
            )

            # Log traditional security event for backward compatibility
            log_security_event(
                unique_id=unique_id,
                event_type="device_registered",
                description=f"Device registered: {device_name}",
                user=user,
                ip_address=ip_address,
                user_agent=user_agent,
                device_id=device_id,
                metadata={
                    "device_type": device_type,
                    "fingerprint": fingerprint,
                    "created": created,
                },
            )

            log_operation_info(
                unique_id,
                "DEVICE_REGISTRATION_SUCCESS",
                f"Device registration successful: {device_name}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device_name,
                    "device_type": device_type,
                    "device_pk": device.pk,
                    "created": created,
                    "fingerprint": fingerprint,
                },
            )

            return device

        except Exception as e:
            # Log database operation failure
            log_database_operation(
                unique_id,
                "CREATE",
                "oauth2_auth_devicetoken",
                operation_result="FAILURE",
                metadata={
                    "user_id": user.id,
                    "device_id": device_id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

            log_operation_info(
                unique_id,
                "DEVICE_REGISTRATION_ERROR",
                f"Device registration failed for user {user.email}: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device_name,
                    "device_type": device_type,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return None

    @staticmethod
    def trust_device(user, device_id, trust_duration_days=30, unique_id=None):
        """
        Mark a device as trusted for a specified duration
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "DEVICE_TRUST_START",
            f"Starting device trust operation for user: {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "device_id": device_id,
                "trust_duration_days": trust_duration_days,
                "operation_type": "device_trust",
            },
        )

        try:
            # Log database operation
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_auth_devicetoken",
                metadata={
                    "user_id": user.id,
                    "device_id": device_id,
                    "query_type": "get_by_user_and_device_id",
                },
            )

            device = DeviceToken.objects.get(user=user, device_id=device_id)

            # Log database operation for update
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_auth_devicetoken",
                metadata={
                    "device_pk": device.pk,
                    "device_id": device_id,
                    "trust_duration_days": trust_duration_days,
                    "operation": "set_device_trust",
                },
            )

            device.is_trusted = True
            device.trust_expires = timezone.now() + timezone.timedelta(
                days=trust_duration_days
            )
            device.save()

            # Log successful database operation
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_auth_devicetoken",
                operation_result="SUCCESS",
                metadata={
                    "device_pk": device.pk,
                    "device_id": device_id,
                    "trust_expires": device.trust_expires.isoformat(),
                },
            )

            # Log security event with standardized logging
            log_security_event_standardized(
                unique_id,
                "DEVICE_TRUSTED",
                f"Device trusted: {device.device_name}",
                user=user,
                metadata={
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "device_pk": device.pk,
                    "trust_duration_days": trust_duration_days,
                    "trust_expires": device.trust_expires.isoformat(),
                },
            )

            # Log business event
            log_business_event(
                unique_id,
                "DEVICE_TRUSTED",
                f"Device trusted for user: {user.email}",
                entity_type="DEVICE",
                entity_id=str(device.pk),
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "trust_duration_days": trust_duration_days,
                    "trust_expires": device.trust_expires.isoformat(),
                },
            )

            # Log traditional security event for backward compatibility
            log_security_event(
                user=user,
                event_type="device_trusted",
                description=f"Device trusted: {device.device_name}",
                device_id=device_id,
                metadata={
                    "trust_duration_days": trust_duration_days,
                    "trust_expires": device.trust_expires.isoformat(),
                },
            )

            log_operation_info(
                unique_id,
                "DEVICE_TRUST_SUCCESS",
                f"Device trust successful: {device.device_name}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "device_pk": device.pk,
                    "trust_duration_days": trust_duration_days,
                    "trust_expires": device.trust_expires.isoformat(),
                },
            )

            return True

        except DeviceToken.DoesNotExist:
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_auth_devicetoken",
                operation_result="NOT_FOUND",
                metadata={
                    "user_id": user.id,
                    "device_id": device_id,
                    "error": "DeviceToken does not exist",
                },
                level="WARNING",
            )

            log_operation_info(
                unique_id,
                "DEVICE_TRUST_NOT_FOUND",
                f"Device not found for trust operation: {device_id}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "error": "DeviceToken does not exist",
                },
                level="WARNING",
            )
            return False

        except Exception as e:
            # Log database operation failure
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_auth_devicetoken",
                operation_result="FAILURE",
                metadata={
                    "user_id": user.id,
                    "device_id": device_id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

            log_operation_info(
                unique_id,
                "DEVICE_TRUST_ERROR",
                f"Device trust failed for user {user.email}: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "trust_duration_days": trust_duration_days,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return False

    @staticmethod
    def revoke_device_trust(user, device_id, unique_id=None):
        """
        Revoke trust for a device
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "DEVICE_TRUST_REVOCATION_START",
            f"Starting device trust revocation for user: {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "device_id": device_id,
                "operation_type": "device_trust_revocation",
            },
        )

        try:
            # Log database operation
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_auth_devicetoken",
                metadata={
                    "user_id": user.id,
                    "device_id": device_id,
                    "query_type": "get_by_user_and_device_id",
                },
            )

            device = DeviceToken.objects.get(user=user, device_id=device_id)

            # Log database operation for update
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_auth_devicetoken",
                metadata={
                    "device_pk": device.pk,
                    "device_id": device_id,
                    "operation": "revoke_device_trust",
                },
            )

            device.is_trusted = False
            device.trust_expires = None
            device.save()

            # Log successful database operation
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_auth_devicetoken",
                operation_result="SUCCESS",
                metadata={
                    "device_pk": device.pk,
                    "device_id": device_id,
                    "trust_revoked": True,
                },
            )

            # Log security event with standardized logging
            log_security_event_standardized(
                unique_id,
                "DEVICE_TRUST_REVOKED",
                f"Device trust revoked: {device.device_name}",
                user=user,
                metadata={
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "device_pk": device.pk,
                    "security_action": "trust_revocation",
                },
            )

            # Log business event
            log_business_event(
                unique_id,
                "DEVICE_TRUST_REVOKED",
                f"Device trust revoked for user: {user.email}",
                entity_type="DEVICE",
                entity_id=str(device.pk),
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "security_action": "trust_revocation",
                },
            )

            # Log traditional security event for backward compatibility
            log_security_event(
                user=user,
                event_type="device_trust_revoked",
                description=f"Device trust revoked: {device.device_name}",
                device_id=device_id,
            )

            log_operation_info(
                unique_id,
                "DEVICE_TRUST_REVOCATION_SUCCESS",
                f"Device trust revocation successful: {device.device_name}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "device_pk": device.pk,
                },
            )

            return True

        except DeviceToken.DoesNotExist:
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_auth_devicetoken",
                operation_result="NOT_FOUND",
                metadata={
                    "user_id": user.id,
                    "device_id": device_id,
                    "error": "DeviceToken does not exist",
                },
                level="WARNING",
            )

            log_operation_info(
                unique_id,
                "DEVICE_TRUST_REVOCATION_NOT_FOUND",
                f"Device not found for trust revocation: {device_id}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "error": "DeviceToken does not exist",
                },
                level="WARNING",
            )
            return False

        except Exception as e:
            # Log database operation failure
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_auth_devicetoken",
                operation_result="FAILURE",
                metadata={
                    "user_id": user.id,
                    "device_id": device_id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

            log_operation_info(
                unique_id,
                "DEVICE_TRUST_REVOCATION_ERROR",
                f"Device trust revocation failed for user {user.email}: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return False


class EnhancedSessionAuthentication(SessionAuthentication):
    """
    Enhanced session authentication with user status validation
    """

    def authenticate(self, request):
        """
        Authenticate using session with user status validation
        """
        import time

        start_time = time.time()

        # Generate unique request ID for logging correlation
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "SESSION_AUTHENTICATION_START",
            "Starting session authentication",
            metadata={
                "client_ip": get_client_ip(request),
                "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "path": request.path,
                "method": request.method,
                "authentication_method": "session",
                "operation_type": "session_authentication",
            },
        )

        # Call parent authentication first
        auth_result = super().authenticate(request)

        if auth_result is None:
            log_operation_info(
                unique_id,
                "SESSION_AUTHENTICATION_SKIPPED",
                "Session authentication skipped - no valid session",
                metadata={
                    "client_ip": get_client_ip(request),
                    "path": request.path,
                    "authentication_result": "skipped",
                },
            )
            return None

        user, session_data = auth_result
        auth_time = time.time() - start_time

        # Validate user status
        UserStatusValidator = _get_user_status_validator()
        validation_result = UserStatusValidator.validate_user_status(
            user, log_violations=True
        )

        if not validation_result["is_valid"]:
            # Log authentication attempt with user status violation
            _log_authentication_attempt(
                user=user,
                success=False,
                method="session",
                ip_address=get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                error_code=validation_result["error_code"],
            )

            log_operation_info(
                unique_id,
                "SESSION_AUTHENTICATION_FAILED",
                f"Session authentication failed for user {user.email}: {validation_result['error_message']}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "authentication_method": "session",
                    "authentication_result": "failed",
                    "failure_reason": "user_status_violation",
                    "error_code": validation_result["error_code"],
                    "authentication_time_ms": auth_time * 1000,
                },
                level="WARNING",
            )

            # Raise authentication failed with specific error
            raise AuthenticationFailed(
                f"{validation_result['error_message']}: {validation_result['error_details']}"
            )

        # Log performance metric
        log_performance_metric(
            unique_id,
            "session_authentication_time",
            auth_time * 1000,  # Convert to milliseconds
            unit="ms",
            operation_type="session_authentication",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
            },
        )

        # Log business event for successful authentication
        log_business_event(
            unique_id,
            "USER_AUTHENTICATED",
            f"User successfully authenticated via session: {user.email}",
            entity_type="USER",
            entity_id=str(user.id),
            metadata={
                "authentication_method": "session",
                "user_email": user.email,
                "authentication_time_ms": auth_time * 1000,
            },
        )

        # Log successful session authentication
        _log_authentication_attempt(
            user=user,
            success=True,
            method="session",
            ip_address=get_client_ip(request),
            user_agent=request.META.get("HTTP_USER_AGENT", ""),
        )

        log_operation_info(
            unique_id,
            "SESSION_AUTHENTICATION_SUCCESS",
            f"Session authentication successful for user: {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "authentication_method": "session",
                "authentication_result": "success",
                "authentication_time_ms": auth_time * 1000,
            },
        )

        return auth_result


class EnhancedTokenAuthentication(TokenAuthentication):
    """
    Enhanced token authentication with user status validation
    """

    def authenticate(self, request):
        """
        Authenticate using token with user status validation
        """
        import time

        start_time = time.time()

        # Generate unique request ID for logging correlation
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "TOKEN_AUTHENTICATION_START",
            "Starting token authentication",
            metadata={
                "client_ip": get_client_ip(request),
                "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "path": request.path,
                "method": request.method,
                "authentication_method": "token",
                "operation_type": "token_authentication",
            },
        )

        # Call parent authentication first
        auth_result = super().authenticate(request)

        if auth_result is None:
            log_operation_info(
                unique_id,
                "TOKEN_AUTHENTICATION_SKIPPED",
                "Token authentication skipped - no valid token",
                metadata={
                    "client_ip": get_client_ip(request),
                    "path": request.path,
                    "authentication_result": "skipped",
                },
            )
            return None

        user, token_data = auth_result
        auth_time = time.time() - start_time

        # Validate user status
        UserStatusValidator = _get_user_status_validator()
        validation_result = UserStatusValidator.validate_user_status(
            user, log_violations=True
        )

        if not validation_result["is_valid"]:
            # Log authentication attempt with user status violation
            _log_authentication_attempt(
                user=user,
                success=False,
                method="token",
                ip_address=get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                error_code=validation_result["error_code"],
            )

            log_operation_info(
                unique_id,
                "TOKEN_AUTHENTICATION_FAILED",
                f"Token authentication failed for user {user.email}: {validation_result['error_message']}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "authentication_method": "token",
                    "authentication_result": "failed",
                    "failure_reason": "user_status_violation",
                    "error_code": validation_result["error_code"],
                    "authentication_time_ms": auth_time * 1000,
                },
                level="WARNING",
            )

            # Raise authentication failed with specific error
            raise AuthenticationFailed(
                f"{validation_result['error_message']}: {validation_result['error_details']}"
            )

        # Log performance metric
        log_performance_metric(
            unique_id,
            "token_authentication_time",
            auth_time * 1000,  # Convert to milliseconds
            unit="ms",
            operation_type="token_authentication",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
            },
        )

        # Log business event for successful authentication
        log_business_event(
            unique_id,
            "USER_AUTHENTICATED",
            f"User successfully authenticated via token: {user.email}",
            entity_type="USER",
            entity_id=str(user.id),
            metadata={
                "authentication_method": "token",
                "user_email": user.email,
                "authentication_time_ms": auth_time * 1000,
            },
        )

        # Log successful token authentication
        _log_authentication_attempt(
            user=user,
            success=True,
            method="token",
            ip_address=get_client_ip(request),
            user_agent=request.META.get("HTTP_USER_AGENT", ""),
        )

        log_operation_info(
            unique_id,
            "TOKEN_AUTHENTICATION_SUCCESS",
            f"Token authentication successful for user: {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "authentication_method": "token",
                "authentication_result": "success",
                "authentication_time_ms": auth_time * 1000,
            },
        )

        return auth_result
