"""
OAuth2 Client Management System
Handles creation, management, and security of OAuth2 applications/clients
"""

import secrets
from typing import Dict, List, Optional, Tuple
from django.contrib.auth import get_user_model
from django.conf import settings
from django.utils import timezone
from oauth2_provider.models import Application
from .utils import log_security_event
from .email_service import email_service
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
    log_database_operation,
    log_security_event_standardized,
    log_business_event,
    create_logging_context,
)

User = get_user_model()


class OAuth2ClientManager:
    """
    Centralized OAuth2 client management system
    """

    # Predefined client configurations for different frontend applications
    FRONTEND_CLIENTS = {
        "farmer": {
            "name": "Agritram Farmer App",
            "redirect_uris": [
                "http://localhost:3000/auth/callback/",
                "https://farmer.agritram.com/auth/callback/",
            ],
            "scopes": ["read", "write", "profile", "email", "farmer"],
            "description": "Frontend application for farmers",
        },
        "trader": {
            "name": "Agritram Trader App",
            "redirect_uris": [
                "http://localhost:3001/auth/callback/",
                "https://trader.agritram.com/auth/callback/",
            ],
            "scopes": ["read", "write", "profile", "email", "trader"],
            "description": "Frontend application for traders",
        },
        "manufacturer": {
            "name": "Agritram Manufacturer App",
            "redirect_uris": [
                "http://localhost:3002/auth/callback/",
                "https://manufacturer.agritram.com/auth/callback/",
            ],
            "scopes": ["read", "write", "profile", "email", "manufacturer"],
            "description": "Frontend application for manufacturers",
        },
        "admin": {
            "name": "Agritram Admin Dashboard",
            "redirect_uris": [
                "http://localhost:3003/auth/callback/",
                "https://admin.agritram.com/auth/callback/",
            ],
            "scopes": ["read", "write", "profile", "email", "admin"],
            "description": "Admin dashboard application",
        },
    }

    def __init__(self):
        self.default_scopes = ["read", "write", "profile", "email"]

    def create_client_for_user(
        self, user, client_type: str = "user", unique_id: Optional[str] = None, **kwargs
    ) -> Application:
        """
        Create an OAuth2 client application for a specific user
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        try:
            log_operation_info(
                unique_id,
                "OAUTH2_CLIENT_CREATION_START",
                f"Starting OAuth2 client creation for user {user.email}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "client_type": client_type,
                    "operation_type": "oauth2_client_creation",
                },
            )
            # Generate secure client credentials
            client_id = self._generate_client_id(user, client_type)
            client_secret = self._generate_client_secret()

            # Determine redirect URIs
            redirect_uris = kwargs.get("redirect_uris", [])
            if not redirect_uris:
                redirect_uris = [f"{settings.FRONTEND_URL}/auth/callback/"]

            # Create application
            application = Application.objects.create(
                name=kwargs.get("name", f"Agritram App - {user.name}"),
                user=user,
                client_type=Application.CLIENT_CONFIDENTIAL,
                authorization_grant_type=Application.GRANT_AUTHORIZATION_CODE,
                client_id=client_id,
                client_secret=client_secret,
                redirect_uris=" ".join(redirect_uris),
                post_logout_redirect_uris=kwargs.get("post_logout_redirect_uris", ""),
                algorithm=kwargs.get("algorithm", "RS256"),
            )

            # Log client creation with traditional security event
            log_security_event(
                user=user,
                event_type="oauth2_client_created",
                description=f"OAuth2 client created: {application.name}",
                metadata={
                    "client_id": client_id,
                    "client_type": client_type,
                    "redirect_uris": redirect_uris,
                    "application_id": application.id,
                },
            )

            # Also log with standardized logging
            log_operation_info(
                unique_id,
                "OAUTH2_CLIENT_CREATED",
                f"OAuth2 client created successfully: {application.name}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "client_id": client_id,
                    "client_type": client_type,
                    "application_id": application.id,
                    "application_name": application.name,
                    "redirect_uris": redirect_uris,
                    "security_event": "oauth2_client_created",
                },
            )

            return application

        except Exception as e:
            # Also log with standardized logging
            log_operation_info(
                unique_id,
                "OAUTH2_CLIENT_CREATION_ERROR",
                f"Failed to create OAuth2 client for user {user.email}: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "client_type": client_type,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            raise

    def create_frontend_client(
        self,
        client_type: str,
        environment: str = "development",
        unique_id: Optional[str] = None,
    ) -> Application:
        """
        Create predefined frontend application clients
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        if client_type not in self.FRONTEND_CLIENTS:
            log_operation_info(
                unique_id,
                "FRONTEND_CLIENT_CREATION_ERROR",
                f"Unknown frontend client type: {client_type}",
                metadata={
                    "client_type": client_type,
                    "available_types": list(self.FRONTEND_CLIENTS.keys()),
                    "error_type": "ValueError",
                },
                level="ERROR",
            )
            raise ValueError(f"Unknown frontend client type: {client_type}")

        config = self.FRONTEND_CLIENTS[client_type]

        log_operation_info(
            unique_id,
            "FRONTEND_CLIENT_CREATION_START",
            f"Starting frontend client creation for type: {client_type}",
            metadata={
                "client_type": client_type,
                "environment": environment,
                "config_name": config["name"],
                "operation_type": "frontend_client_creation",
            },
        )

        try:
            # Check if client already exists
            existing_client = Application.objects.filter(
                name=config["name"], client_type=Application.CLIENT_CONFIDENTIAL
            ).first()

            if existing_client:
                log_operation_info(
                    unique_id,
                    "FRONTEND_CLIENT_EXISTS",
                    f"Frontend client {client_type} already exists",
                    metadata={
                        "client_type": client_type,
                        "client_id": existing_client.client_id,
                        "application_id": existing_client.id,
                        "existing_client": True,
                    },
                )
                return existing_client

            # Generate client credentials
            client_id = f"agritram-{client_type}-{secrets.token_urlsafe(8)}"
            client_secret = self._generate_client_secret()

            # Filter redirect URIs based on environment
            redirect_uris = config["redirect_uris"]
            if environment == "production":
                redirect_uris = [
                    uri
                    for uri in redirect_uris
                    if not uri.startswith("http://localhost")
                ]

            # Log database operation start
            log_database_operation(
                unique_id,
                "CREATE",
                "oauth2_provider_application",
                metadata={
                    "client_type": client_type,
                    "client_id": client_id,
                    "environment": environment,
                },
            )

            # Create application
            application = Application.objects.create(
                name=config["name"],
                client_type=Application.CLIENT_CONFIDENTIAL,
                authorization_grant_type=Application.GRANT_AUTHORIZATION_CODE,
                client_id=client_id,
                client_secret=client_secret,
                redirect_uris=" ".join(redirect_uris),
                algorithm="RS256",
            )

            # Log successful database operation
            log_database_operation(
                unique_id,
                "CREATE",
                "oauth2_provider_application",
                operation_result="SUCCESS",
                metadata={
                    "client_type": client_type,
                    "client_id": client_id,
                    "application_id": application.id,
                    "environment": environment,
                },
            )

            # Log security event with standardized logging
            log_security_event_standardized(
                unique_id,
                "FRONTEND_CLIENT_CREATED",
                f'Frontend OAuth2 client created: {config["name"]}',
                metadata={
                    "client_id": client_id,
                    "client_type": client_type,
                    "environment": environment,
                    "redirect_uris": redirect_uris,
                    "scopes": config["scopes"],
                    "application_id": application.id,
                    "application_name": config["name"],
                },
            )

            # Log business event
            log_business_event(
                unique_id,
                "FRONTEND_CLIENT_CREATED",
                f"New frontend OAuth2 client created for {client_type} environment: {environment}",
                entity_type="OAUTH2_APPLICATION",
                entity_id=str(application.id),
                metadata={
                    "client_type": client_type,
                    "environment": environment,
                    "client_id": client_id,
                    "scopes": config["scopes"],
                },
            )

            # Log traditional security event for backward compatibility
            log_security_event(
                event_type="frontend_client_created",
                description=f'Frontend OAuth2 client created: {config["name"]}',
                metadata={
                    "client_id": client_id,
                    "client_type": client_type,
                    "environment": environment,
                    "redirect_uris": redirect_uris,
                    "scopes": config["scopes"],
                    "application_id": application.id,
                },
            )

            log_operation_info(
                unique_id,
                "FRONTEND_CLIENT_CREATED",
                f"Frontend client created successfully: {config['name']}",
                metadata={
                    "client_type": client_type,
                    "client_id": client_id,
                    "application_id": application.id,
                    "environment": environment,
                    "redirect_uris": redirect_uris,
                    "scopes": config["scopes"],
                },
            )

            return application

        except Exception as e:
            # Log database operation failure
            log_database_operation(
                unique_id,
                "CREATE",
                "oauth2_provider_application",
                operation_result="FAILURE",
                metadata={
                    "client_type": client_type,
                    "environment": environment,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

            log_operation_info(
                unique_id,
                "FRONTEND_CLIENT_CREATION_ERROR",
                f"Failed to create frontend client {client_type}: {str(e)}",
                metadata={
                    "client_type": client_type,
                    "environment": environment,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            raise

    def rotate_client_secret(
        self, application: Application, user=None, unique_id: Optional[str] = None
    ) -> str:
        """
        Rotate client secret for security
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "CLIENT_SECRET_ROTATION_START",
            f"Starting client secret rotation for application: {application.name}",
            metadata={
                "client_id": application.client_id,
                "application_id": application.id,
                "application_name": application.name,
                "rotated_by": user.email if user else "system",
                "operation_type": "client_secret_rotation",
            },
        )

        try:
            old_secret = application.client_secret
            new_secret = self._generate_client_secret()

            # Log database operation start
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_provider_application",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "field_updated": "client_secret",
                },
            )

            application.client_secret = new_secret
            application.save(update_fields=["client_secret"])

            # Log successful database operation
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_provider_application",
                operation_result="SUCCESS",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "field_updated": "client_secret",
                },
            )

            # Log security event with standardized logging
            log_security_event_standardized(
                unique_id,
                "CLIENT_SECRET_ROTATED",
                f"OAuth2 client secret rotated: {application.name}",
                user=user or application.user,
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "application_name": application.name,
                    "rotated_by": user.email if user else "system",
                },
            )

            # Log business event
            log_business_event(
                unique_id,
                "CLIENT_SECRET_ROTATED",
                f"OAuth2 client secret rotated for security: {application.name}",
                entity_type="OAUTH2_APPLICATION",
                entity_id=str(application.id),
                metadata={
                    "client_id": application.client_id,
                    "rotated_by": user.email if user else "system",
                    "security_action": "secret_rotation",
                },
            )

            # Log traditional security event for backward compatibility
            log_security_event(
                user=user or application.user,
                event_type="client_secret_rotated",
                description=f"OAuth2 client secret rotated: {application.name}",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "rotated_by": user.email if user else "system",
                },
            )

            # Send notification email if user exists
            if application.user:
                email_service.send_security_alert(
                    application.user,
                    "OAuth2 Client Secret Rotated",
                    f'The client secret for your OAuth2 application "{application.name}" has been rotated for security reasons.',
                    {
                        "client_id": application.client_id,
                        "application_name": application.name,
                    },
                )

            log_operation_info(
                unique_id,
                "CLIENT_SECRET_ROTATED",
                f"Client secret rotated successfully for {application.client_id}",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "application_name": application.name,
                    "rotated_by": user.email if user else "system",
                    "email_notification_sent": bool(application.user),
                },
            )

            return new_secret

        except Exception as e:
            # Log database operation failure
            log_database_operation(
                unique_id,
                "UPDATE",
                "oauth2_provider_application",
                operation_result="FAILURE",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "field_updated": "client_secret",
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

            log_operation_info(
                unique_id,
                "CLIENT_SECRET_ROTATION_ERROR",
                f"Failed to rotate client secret for {application.client_id}: {str(e)}",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                    "rotated_by": user.email if user else "system",
                },
                level="ERROR",
            )
            raise

    def revoke_client(
        self,
        application: Application,
        reason: str = "User request",
        user=None,
        unique_id: Optional[str] = None,
    ) -> bool:
        """
        Revoke OAuth2 client and all associated tokens
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "CLIENT_REVOCATION_START",
            f"Starting client revocation for application: {application.name}",
            metadata={
                "client_id": application.client_id,
                "application_id": application.id,
                "application_name": application.name,
                "reason": reason,
                "revoked_by": user.email if user else "system",
                "operation_type": "client_revocation",
            },
        )

        try:
            from oauth2_provider.models import AccessToken, RefreshToken, Grant

            # Count tokens before deletion for logging
            access_token_count = AccessToken.objects.filter(
                application=application
            ).count()
            refresh_token_count = RefreshToken.objects.filter(
                application=application
            ).count()
            grant_count = Grant.objects.filter(application=application).count()

            # Log database operations for token cleanup
            log_database_operation(
                unique_id,
                "DELETE",
                "oauth2_provider_accesstoken",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "token_count": access_token_count,
                },
            )

            # Revoke all tokens
            AccessToken.objects.filter(application=application).delete()
            RefreshToken.objects.filter(application=application).delete()
            Grant.objects.filter(application=application).delete()

            # Log successful token cleanup
            log_database_operation(
                unique_id,
                "DELETE",
                "oauth2_provider_tokens",
                operation_result="SUCCESS",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "access_tokens_deleted": access_token_count,
                    "refresh_tokens_deleted": refresh_token_count,
                    "grants_deleted": grant_count,
                },
            )

            # Log security event with standardized logging
            log_security_event_standardized(
                unique_id,
                "CLIENT_REVOKED",
                f"OAuth2 client revoked: {application.name}",
                user=user or application.user,
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "application_name": application.name,
                    "reason": reason,
                    "revoked_by": user.email if user else "system",
                    "tokens_revoked": {
                        "access_tokens": access_token_count,
                        "refresh_tokens": refresh_token_count,
                        "grants": grant_count,
                    },
                },
            )

            # Log business event
            log_business_event(
                unique_id,
                "CLIENT_REVOKED",
                f"OAuth2 client revoked: {application.name} - Reason: {reason}",
                entity_type="OAUTH2_APPLICATION",
                entity_id=str(application.id),
                metadata={
                    "client_id": application.client_id,
                    "reason": reason,
                    "revoked_by": user.email if user else "system",
                    "tokens_cleaned": access_token_count
                    + refresh_token_count
                    + grant_count,
                },
            )

            # Log traditional security event for backward compatibility
            log_security_event(
                user=user or application.user,
                event_type="oauth2_client_revoked",
                description=f"OAuth2 client revoked: {application.name}",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "reason": reason,
                    "revoked_by": user.email if user else "system",
                },
            )

            # Send notification email
            if application.user:
                email_service.send_security_alert(
                    application.user,
                    "OAuth2 Application Revoked",
                    f'Your OAuth2 application "{application.name}" has been revoked. Reason: {reason}',
                    {"client_id": application.client_id, "reason": reason},
                )

            # Log database operation for application deletion
            log_database_operation(
                unique_id,
                "DELETE",
                "oauth2_provider_application",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                },
            )

            # Delete application
            application.delete()

            # Log successful application deletion
            log_database_operation(
                unique_id,
                "DELETE",
                "oauth2_provider_application",
                operation_result="SUCCESS",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                },
            )

            log_operation_info(
                unique_id,
                "CLIENT_REVOKED",
                f"OAuth2 client revoked successfully: {application.client_id}",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "reason": reason,
                    "revoked_by": user.email if user else "system",
                    "tokens_cleaned": access_token_count
                    + refresh_token_count
                    + grant_count,
                    "email_notification_sent": bool(application.user),
                },
            )

            return True

        except Exception as e:
            # Log database operation failure
            log_database_operation(
                unique_id,
                "DELETE",
                "oauth2_provider_application",
                operation_result="FAILURE",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

            log_operation_info(
                unique_id,
                "CLIENT_REVOCATION_ERROR",
                f"Failed to revoke OAuth2 client {application.client_id}: {str(e)}",
                metadata={
                    "client_id": application.client_id,
                    "application_id": application.id,
                    "reason": reason,
                    "revoked_by": user.email if user else "system",
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return False

    def get_client_info(
        self, client_id: str, unique_id: Optional[str] = None
    ) -> Optional[Dict]:
        """
        Get comprehensive client information
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "CLIENT_INFO_RETRIEVAL_START",
            f"Starting client info retrieval for client_id: {client_id}",
            metadata={
                "client_id": client_id,
                "operation_type": "client_info_retrieval",
            },
        )

        try:
            # Log database operation
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_application",
                metadata={
                    "client_id": client_id,
                    "query_type": "get_by_client_id",
                },
            )

            application = Application.objects.get(client_id=client_id)

            # Get token statistics
            from oauth2_provider.models import AccessToken, RefreshToken

            active_tokens = AccessToken.objects.filter(
                application=application, expires__gt=timezone.now()
            ).count()

            total_tokens = AccessToken.objects.filter(application=application).count()

            client_info = {
                "client_id": application.client_id,
                "name": application.name,
                "client_type": application.get_client_type_display(),
                "grant_type": application.get_authorization_grant_type_display(),
                "redirect_uris": application.redirect_uris.split(),
                "created": application.created,
                "updated": application.updated,
                "user": application.user.email if application.user else None,
                "active_tokens": active_tokens,
                "total_tokens_issued": total_tokens,
                "algorithm": application.algorithm,
            }

            # Log successful database operation
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_application",
                operation_result="SUCCESS",
                metadata={
                    "client_id": client_id,
                    "application_id": application.id,
                    "active_tokens": active_tokens,
                    "total_tokens": total_tokens,
                },
            )

            log_operation_info(
                unique_id,
                "CLIENT_INFO_RETRIEVED",
                f"Client info retrieved successfully for {client_id}",
                metadata={
                    "client_id": client_id,
                    "application_id": application.id,
                    "application_name": application.name,
                    "active_tokens": active_tokens,
                    "total_tokens": total_tokens,
                },
            )

            return client_info

        except Application.DoesNotExist:
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_application",
                operation_result="NOT_FOUND",
                metadata={
                    "client_id": client_id,
                    "error": "Application does not exist",
                },
                level="WARNING",
            )

            log_operation_info(
                unique_id,
                "CLIENT_INFO_NOT_FOUND",
                f"Client not found for client_id: {client_id}",
                metadata={
                    "client_id": client_id,
                    "error": "Application does not exist",
                },
                level="WARNING",
            )
            return None

        except Exception as e:
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_application",
                operation_result="FAILURE",
                metadata={
                    "client_id": client_id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

            log_operation_info(
                unique_id,
                "CLIENT_INFO_RETRIEVAL_ERROR",
                f"Failed to get client info for {client_id}: {str(e)}",
                metadata={
                    "client_id": client_id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return None

    def list_user_clients(self, user, unique_id: Optional[str] = None) -> List[Dict]:
        """
        List all OAuth2 clients for a user
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id,
            "USER_CLIENTS_LIST_START",
            f"Starting client list retrieval for user: {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "operation_type": "user_clients_list",
            },
        )

        try:
            # Log database operation
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_application",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "query_type": "filter_by_user",
                },
            )

            applications = Application.objects.filter(user=user)
            client_count = applications.count()

            # Get detailed info for each client
            client_list = []
            for app in applications:
                client_info = self.get_client_info(app.client_id, unique_id)
                if client_info:
                    client_list.append(client_info)

            # Log successful database operation
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_application",
                operation_result="SUCCESS",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "client_count": client_count,
                },
            )

            log_operation_info(
                unique_id,
                "USER_CLIENTS_LISTED",
                f"Client list retrieved successfully for user {user.email}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "client_count": client_count,
                    "clients_retrieved": len(client_list),
                },
            )

            return client_list

        except Exception as e:
            log_database_operation(
                unique_id,
                "SELECT",
                "oauth2_provider_application",
                operation_result="FAILURE",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

            log_operation_info(
                unique_id,
                "USER_CLIENTS_LIST_ERROR",
                f"Failed to list clients for user {user.email}: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return []

    def _generate_client_id(self, user, client_type: str) -> str:
        """
        Generate a unique client ID
        """
        base = f"agritram-{client_type}-{user.id}"
        suffix = secrets.token_urlsafe(8)
        return f"{base}-{suffix}"

    def _generate_client_secret(self) -> str:
        """
        Generate a secure client secret
        """
        return secrets.token_urlsafe(32)

    def setup_default_clients(self, unique_id: Optional[str] = None) -> Dict[str, str]:
        """
        Set up default OAuth2 clients for all frontend applications
        """
        # Generate unique ID if not provided
        if not unique_id:
            unique_id = generate_unique_request_id()

        environment = "development" if settings.DEBUG else "production"

        log_operation_info(
            unique_id,
            "DEFAULT_CLIENTS_SETUP_START",
            f"Starting default clients setup for environment: {environment}",
            metadata={
                "environment": environment,
                "client_types": list(self.FRONTEND_CLIENTS.keys()),
                "operation_type": "default_clients_setup",
            },
        )

        results = {}
        successful_setups = 0
        failed_setups = 0

        for client_type in self.FRONTEND_CLIENTS.keys():
            try:
                log_operation_info(
                    unique_id,
                    "DEFAULT_CLIENT_SETUP_ATTEMPT",
                    f"Setting up default client for type: {client_type}",
                    metadata={
                        "client_type": client_type,
                        "environment": environment,
                    },
                )

                application = self.create_frontend_client(
                    client_type, environment, unique_id
                )
                results[client_type] = application.client_id
                successful_setups += 1

                log_operation_info(
                    unique_id,
                    "DEFAULT_CLIENT_SETUP_SUCCESS",
                    f"Successfully set up default client for type: {client_type}",
                    metadata={
                        "client_type": client_type,
                        "client_id": application.client_id,
                        "application_id": application.id,
                        "environment": environment,
                    },
                )

            except Exception as e:
                failed_setups += 1
                error_message = f"ERROR: {str(e)}"
                results[client_type] = error_message

                log_operation_info(
                    unique_id,
                    "DEFAULT_CLIENT_SETUP_ERROR",
                    f"Failed to create default client {client_type}: {str(e)}",
                    metadata={
                        "client_type": client_type,
                        "environment": environment,
                        "error_message": str(e),
                        "error_type": e.__class__.__name__,
                    },
                    level="ERROR",
                )

        # Log business event for the overall setup operation
        log_business_event(
            unique_id,
            "DEFAULT_CLIENTS_SETUP_COMPLETED",
            f"Default OAuth2 clients setup completed for {environment} environment",
            entity_type="OAUTH2_SETUP",
            entity_id=unique_id,
            metadata={
                "environment": environment,
                "total_clients": len(self.FRONTEND_CLIENTS),
                "successful_setups": successful_setups,
                "failed_setups": failed_setups,
                "results": results,
            },
            level="ERROR" if failed_setups > 0 else "INFO",
        )

        log_operation_info(
            unique_id,
            "DEFAULT_CLIENTS_SETUP_COMPLETED",
            f"Default clients setup completed: {successful_setups} successful, {failed_setups} failed",
            metadata={
                "environment": environment,
                "total_clients": len(self.FRONTEND_CLIENTS),
                "successful_setups": successful_setups,
                "failed_setups": failed_setups,
                "results": results,
            },
            level="ERROR" if failed_setups > 0 else "INFO",
        )

        return results


# Global client manager instance
oauth2_client_manager = OAuth2ClientManager()
