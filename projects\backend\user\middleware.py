from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import logout
from django.http import JsonR<PERSON>ponse
from django.utils import timezone
from .auth_utils import UserStatusValidator
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
    log_security_event_standardized,
)


class UserStatusValidationMiddleware(MiddlewareMixin):
    """
    Middleware to continuously validate user status during requests.
    Automatically logs out users who become inactive, deleted, or locked.
    """

    EXCLUDED_PATHS = [
        "/admin/",
        "/static/",
        "/media/",
        "/health/",
        "/favicon.ico",
        "/api/auth/login/",
        "/api/auth/logout/",
        "/api/auth/register/",
        "/api/auth/password-reset/",
        "/o/",  # OAuth2 endpoints
    ]

    def process_request(self, request):
        """
        Check user status before processing the request.
        """
        # Generate unique ID for logging correlation
        unique_id = generate_unique_request_id()

        # Skip checks for excluded paths
        if any(request.path.startswith(path) for path in self.EXCLUDED_PATHS):
            return None

        # Skip if user is not authenticated
        if not hasattr(request, "user") or not request.user.is_authenticated:
            return None

        user = request.user

        # Use centralized user status validation
        validation_result = UserStatusValidator.validate_user_status(
            user, log_violations=True
        )

        if not validation_result["is_valid"]:
            # Log the user out
            logout(request)

            # Get error details from validation result
            error_code = validation_result["error_code"]
            error_message = validation_result["error_message"]
            error_details = validation_result["error_details"]

            # Log security event for user status violation
            log_security_event_standardized(
                unique_id,
                "USER_STATUS_VIOLATION_LOGOUT",
                f"User logged out due to status violation: {error_message}",
                user=user,
                request=request,
                metadata={
                    "error_code": error_code,
                    "error_message": error_message,
                    "error_details": error_details,
                    "validation_result": validation_result,
                },
                level="WARNING",
            )

            # Return appropriate response based on request type
            if (
                request.path.startswith("/api/")
                or request.content_type == "application/json"
            ):
                return JsonResponse(
                    {
                        "code": error_code,
                        "message": error_message,
                        "details": error_details,
                        "actions": ["contact_support", "login_different_account"],
                    },
                    status=403,
                )
            else:
                # For web requests, redirect to login with message
                from django.shortcuts import redirect
                from django.contrib import messages

                messages.error(request, error_details)
                return redirect("login")

        return None

    def process_response(self, request, response):
        """
        Update user's online status and last activity.
        """
        # Generate unique ID for logging correlation
        unique_id = generate_unique_request_id()

        # Skip for excluded paths and unauthenticated users
        if any(request.path.startswith(path) for path in self.EXCLUDED_PATHS):
            return response

        if not hasattr(request, "user") or not request.user.is_authenticated:
            return response

        # Update user's online status and last activity
        try:
            user = request.user
            now = timezone.now()

            # Update online status and last activity
            # Only update if it's been more than 5 minutes since last update to reduce DB writes
            if (
                not hasattr(user, "_last_activity_update")
                or (now - user._last_activity_update).total_seconds() > 300
            ):
                user.last_login = now  # Use last_login as last_activity
                user.save(update_fields=["last_login"])
                user._last_activity_update = now

                # Log user activity update
                log_operation_info(
                    unique_id,
                    "USER_ACTIVITY_UPDATE",
                    f"Updated last activity for user: {user.email}",
                    metadata={
                        "user_id": user.id,
                        "user_email": user.email,
                        "last_activity": now.isoformat(),
                        "endpoint": request.path,
                    },
                )

        except Exception as e:
            # Log error with standardized logging
            log_operation_info(
                unique_id,
                "USER_ACTIVITY_UPDATE_ERROR",
                f"Error updating user activity for {user.email if hasattr(request, 'user') and request.user.is_authenticated else 'unknown'}: {str(e)}",
                metadata={
                    "error": str(e),
                    "user_id": (
                        user.id
                        if hasattr(request, "user") and request.user.is_authenticated
                        else None
                    ),
                    "endpoint": request.path,
                },
                level="ERROR",
            )

        return response


# TODO:  UserSessionTimeoutMiddleware to updtate user online status when session expires


class UserStatusAuditMiddleware(MiddlewareMixin):
    """
    Middleware to audit user status-related events and access patterns.
    """

    def process_request(self, request):
        """
        Log access attempts by users with restricted status.
        """
        # Generate unique ID for logging correlation
        unique_id = generate_unique_request_id()

        if not hasattr(request, "user") or not request.user.is_authenticated:
            return None

        user = request.user

        # Use centralized user status summary for consistent status checking
        user_status = UserStatusValidator.get_user_status_summary(user)

        # Build status flags list based on the centralized status summary
        status_flags = []
        if user_status.get("is_deleted", False):
            status_flags.append("deleted")
        if not user_status.get("is_active", True):
            status_flags.append("inactive")
        if not user_status.get("is_mail_verified", True):
            status_flags.append("unverified_email")
        if user_status.get("is_locked", False):
            status_flags.append("locked")

        if status_flags:
            # Log security event for restricted user access
            log_security_event_standardized(
                unique_id,
                "RESTRICTED_USER_ACCESS_ATTEMPT",
                f"Access attempt by user with status flags {status_flags}: {user.email} -> {request.path}",
                user=user,
                request=request,
                metadata={
                    "status_flags": status_flags,
                    "user_status": user_status,
                    "endpoint": request.path,
                    "method": request.method,
                },
                level="INFO",
            )

        return None
