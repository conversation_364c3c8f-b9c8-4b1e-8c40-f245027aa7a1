import re
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from rest_framework.exceptions import ValidationError
import ipaddress
from .utils import get_client_ip
from agritram.logger_utils import (
    generate_unique_request_id,
    log_request_info,
    log_response_info,
    log_operation_info,
    log_security_event_standardized,
    create_logging_context,
)


# TODO: and use Implement a more robust solution for this.
class SecurityValidationMixin:
    """
    Mixin for comprehensive input validation and sanitization
    """

    @staticmethod
    def validate_email(email):
        """
        Validate email format with enhanced security checks
        """
        unique_id = generate_unique_request_id()

        if not email:
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="EMAIL_VALIDATION_FAILURE",
                description="Email validation failed: Email is required",
                metadata={"validation_error": "missing_email"},
                level="WARNING",
            )
            raise ValidationError("Email is required")

        # Basic email regex
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, email):
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="EMAIL_VALIDATION_FAILURE",
                description=f"Email validation failed: Invalid format for {email}",
                metadata={"validation_error": "invalid_format", "email": email},
                level="WARNING",
            )
            raise ValidationError("Invalid email format")

        # Check for suspicious patterns
        suspicious_patterns = [
            r'[<>"\']',  # HTML/script injection
            r"javascript:",  # JavaScript injection
            r"data:",  # Data URI
            r"vbscript:",  # VBScript injection
        ]

        for pattern in suspicious_patterns:
            if re.search(pattern, email, re.IGNORECASE):
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="EMAIL_SECURITY_VIOLATION",
                    description=f"Email contains suspicious content: {email}",
                    metadata={
                        "validation_error": "suspicious_content",
                        "email": email,
                        "pattern_matched": pattern,
                    },
                    level="ERROR",
                )
                raise ValidationError("Email contains suspicious content")

        log_operation_info(
            unique_id=unique_id,
            operation_type="EMAIL_VALIDATION_SUCCESS",
            message=f"Email validation successful for {email}",
            metadata={"email": email},
        )
        return email.lower().strip()

    @staticmethod
    def validate_password(password):
        """
        Validate password strength and security
        """
        unique_id = generate_unique_request_id()

        if not password:
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="PASSWORD_VALIDATION_FAILURE",
                description="Password validation failed: Password is required",
                metadata={"validation_error": "missing_password"},
                level="WARNING",
            )
            raise ValidationError("Password is required")

        if len(password) < 8:
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="PASSWORD_VALIDATION_FAILURE",
                description="Password validation failed: Too short",
                metadata={"validation_error": "too_short", "length": len(password)},
                level="WARNING",
            )
            raise ValidationError("Password must be at least 8 characters long")

        if len(password) > 128:
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="PASSWORD_VALIDATION_FAILURE",
                description="Password validation failed: Too long",
                metadata={"validation_error": "too_long", "length": len(password)},
                level="WARNING",
            )
            raise ValidationError("Password is too long")

        # Check for common patterns
        if password.lower() in ["password", "12345678", "qwerty123"]:
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="PASSWORD_SECURITY_VIOLATION",
                description="Password validation failed: Common password detected",
                metadata={"validation_error": "common_password"},
                level="ERROR",
            )
            raise ValidationError("Password is too common")

        # Check for minimum complexity
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

        complexity_score = sum([has_upper, has_lower, has_digit, has_special])
        if complexity_score < 3:
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="PASSWORD_VALIDATION_FAILURE",
                description="Password validation failed: Insufficient complexity",
                metadata={
                    "validation_error": "insufficient_complexity",
                    "complexity_score": complexity_score,
                    "has_upper": has_upper,
                    "has_lower": has_lower,
                    "has_digit": has_digit,
                    "has_special": has_special,
                },
                level="WARNING",
            )
            raise ValidationError(
                "Password must contain at least 3 of: uppercase, lowercase, digits, special characters"
            )

        log_operation_info(
            unique_id=unique_id,
            operation_type="PASSWORD_VALIDATION_SUCCESS",
            message="Password validation successful",
            metadata={"complexity_score": complexity_score},
        )
        return password

    @staticmethod
    def validate_client_id(client_id):
        """
        Validate OAuth2 client ID format
        """
        if not client_id:
            raise ValidationError("Client ID is required")

        # Client ID should be alphanumeric with limited special characters
        if not re.match(r"^[a-zA-Z0-9._-]+$", client_id):
            raise ValidationError("Invalid client ID format")

        if len(client_id) < 10 or len(client_id) > 100:
            raise ValidationError(
                "Client ID length must be between 10 and 100 characters"
            )

        return client_id

    @staticmethod
    def validate_scope(scope):
        """
        Validate OAuth2 scope format
        """
        if not scope:
            return ""

        # Scopes should be space-separated alphanumeric strings
        scopes = scope.split()
        valid_scope_pattern = r"^[a-zA-Z0-9_-]+$"

        for s in scopes:
            if not re.match(valid_scope_pattern, s):
                raise ValidationError(f"Invalid scope format: {s}")

        return scope

    @staticmethod
    def validate_redirect_uri(uri):
        """
        Validate redirect URI for security
        """
        if not uri:
            raise ValidationError("Redirect URI is required")

        # Check for valid URI format
        uri_pattern = r"^https?://[a-zA-Z0-9.-]+(?:\:[0-9]+)?(?:/[^\s]*)?$"
        if not re.match(uri_pattern, uri):
            raise ValidationError("Invalid redirect URI format")

        # Security checks
        if uri.startswith("http://") and not settings.DEBUG:
            raise ValidationError("HTTP redirect URIs are not allowed in production")

        # Check for suspicious patterns
        suspicious_patterns = [
            r"javascript:",
            r"data:",
            r"vbscript:",
            r"file:",
            r"ftp:",
        ]

        for pattern in suspicious_patterns:
            if re.search(pattern, uri, re.IGNORECASE):
                raise ValidationError("Redirect URI contains suspicious scheme")

        return uri

    @staticmethod
    def sanitize_string(value, max_length=255):
        """
        Sanitize string input
        """
        if not value:
            return ""

        # Remove null bytes and control characters
        value = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", str(value))

        # Limit length
        if len(value) > max_length:
            value = value[:max_length]

        return value.strip()


class IPWhitelistMiddleware(MiddlewareMixin):
    """
    IP whitelist/blacklist middleware for enhanced security
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.blacklisted_ips = getattr(settings, "BLACKLISTED_IPS", [])
        self.whitelisted_ips = getattr(settings, "WHITELISTED_IPS", [])
        super().__init__(get_response)

    def process_request(self, request):
        """
        Check IP against whitelist/blacklist
        """
        unique_id = generate_unique_request_id()
        client_ip = get_client_ip(request)

        # Log request information for audit trail
        log_request_info(unique_id, request, "IP_WHITELIST_CHECK")

        # Check blacklist
        if self.is_ip_blacklisted(client_ip):
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="BLACKLISTED_IP_ACCESS",
                description=f"Access attempt from blacklisted IP: {client_ip}",
                request=request,
                metadata={
                    "path": request.path,
                    "client_ip": client_ip,
                    "action": "blocked",
                },
                level="ERROR",
            )

            response = JsonResponse(
                {"error": "access_denied", "error_description": "Access denied"},
                status=403,
            )
            log_response_info(
                unique_id, {"error": "access_denied"}, 403, "IP_BLACKLIST_BLOCK"
            )
            return response

        # Check whitelist for sensitive endpoints
        if self.requires_whitelisted_ip(request.path) and not self.is_ip_whitelisted(
            client_ip
        ):
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="NON_WHITELISTED_IP_ACCESS",
                description=f"Access attempt to sensitive endpoint from non-whitelisted IP: {client_ip}",
                request=request,
                metadata={
                    "path": request.path,
                    "client_ip": client_ip,
                    "action": "blocked",
                    "endpoint_type": "sensitive",
                },
                level="WARNING",
            )

            response = JsonResponse(
                {"error": "access_denied", "error_description": "Access denied"},
                status=403,
            )
            log_response_info(
                unique_id, {"error": "access_denied"}, 403, "IP_WHITELIST_BLOCK"
            )
            return response

        # Log successful IP validation
        log_operation_info(
            unique_id=unique_id,
            operation_type="IP_VALIDATION_SUCCESS",
            message=f"IP validation successful for {client_ip}",
            metadata={
                "client_ip": client_ip,
                "path": request.path,
                "is_sensitive_endpoint": self.requires_whitelisted_ip(request.path),
            },
        )
        return None

    def is_ip_blacklisted(self, ip):
        """
        Check if IP is blacklisted
        """
        try:
            ip_obj = ipaddress.ip_address(ip)
            for blacklisted in self.blacklisted_ips:
                if ip_obj in ipaddress.ip_network(blacklisted, strict=False):
                    return True
        except ValueError:
            pass
        return False

    def is_ip_whitelisted(self, ip):
        """
        Check if IP is whitelisted
        """
        if not self.whitelisted_ips:
            return True  # No whitelist configured

        try:
            ip_obj = ipaddress.ip_address(ip)
            for whitelisted in self.whitelisted_ips:
                if ip_obj in ipaddress.ip_network(whitelisted, strict=False):
                    return True
        except ValueError:
            pass
        return False

    def requires_whitelisted_ip(self, path):
        """
        Check if path requires whitelisted IP
        """
        sensitive_paths = [
            "/admin/",
            "/o/applications/",  # OAuth2 application management
        ]
        return any(
            path.startswith(sensitive_path) for sensitive_path in sensitive_paths
        )


class RequestValidationMiddleware(MiddlewareMixin):
    """
    Middleware for comprehensive request validation
    """

    def process_request(self, request):
        """
        Validate incoming requests for security issues
        """
        unique_id = generate_unique_request_id()

        # Log request information for audit trail
        log_request_info(unique_id, request, "REQUEST_VALIDATION")

        # Check request size
        if hasattr(request, "META") and "CONTENT_LENGTH" in request.META:
            try:
                content_length = int(request.META["CONTENT_LENGTH"])
                max_size = getattr(
                    settings, "MAX_REQUEST_SIZE", 10 * 1024 * 1024
                )  # 10MB default

                if content_length > max_size:
                    log_security_event_standardized(
                        unique_id=unique_id,
                        event_type="OVERSIZED_REQUEST",
                        description=f"Oversized request: {content_length} bytes",
                        request=request,
                        metadata={
                            "content_length": content_length,
                            "max_size": max_size,
                            "path": request.path,
                            "action": "blocked",
                        },
                        level="ERROR",
                    )

                    response = JsonResponse(
                        {
                            "error": "request_too_large",
                            "error_description": "Request size exceeds maximum allowed",
                        },
                        status=413,
                    )
                    log_response_info(
                        unique_id,
                        {"error": "request_too_large"},
                        413,
                        "REQUEST_SIZE_BLOCK",
                    )
                    return response
            except ValueError:
                log_operation_info(
                    unique_id=unique_id,
                    operation_type="REQUEST_SIZE_VALIDATION_ERROR",
                    message="Invalid CONTENT_LENGTH header",
                    metadata={
                        "content_length_header": request.META.get("CONTENT_LENGTH", "")
                    },
                    level="WARNING",
                )

        # Check for suspicious headers
        suspicious_headers = [
            "X-Forwarded-Host",
            "X-Original-URL",
            "X-Rewrite-URL",
        ]

        for header in suspicious_headers:
            if header in request.META:
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="SUSPICIOUS_HEADER",
                    description=f"Suspicious header detected: {header}",
                    request=request,
                    metadata={
                        "header": header,
                        "value": request.META[header],
                        "path": request.path,
                    },
                    level="WARNING",
                )

        # Check User-Agent
        user_agent = request.META.get("HTTP_USER_AGENT", "")
        if not user_agent or len(user_agent) < 10:
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="SUSPICIOUS_USER_AGENT",
                description=f"Suspicious or missing User-Agent: {user_agent}",
                request=request,
                metadata={"path": request.path, "user_agent_length": len(user_agent)},
                level="WARNING",
            )

        # Log successful request validation
        log_operation_info(
            unique_id=unique_id,
            operation_type="REQUEST_VALIDATION_SUCCESS",
            message="Request validation completed successfully",
            metadata={
                "path": request.path,
                "method": request.method,
                "content_length": request.META.get("CONTENT_LENGTH", "0"),
            },
        )
        return None


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers to all responses
    """

    def process_response(self, request, response):
        """
        Add security headers to response
        """
        # Content Security Policy
        response["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )

        # Security headers
        response["X-Content-Type-Options"] = "nosniff"
        response["X-Frame-Options"] = "DENY"
        response["X-XSS-Protection"] = "1; mode=block"
        response["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response["Permissions-Policy"] = (
            "geolocation=(), microphone=(), camera=(), "
            "payment=(), usb=(), magnetometer=(), gyroscope=()"
        )

        # HSTS (only for HTTPS)
        if request.is_secure():
            response["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains; preload"
            )

        return response


class AuditLoggingMiddleware(MiddlewareMixin):
    """
    Comprehensive audit logging middleware
    """

    def process_request(self, request):
        """
        Log request details for audit purposes
        """
        # Skip logging for static files and health checks
        skip_paths = ["/static/", "/media/", "/health/", "/favicon.ico"]
        if any(request.path.startswith(path) for path in skip_paths):
            return None

        unique_id = generate_unique_request_id()

        # Log all requests for comprehensive audit trail
        log_request_info(unique_id, request, "AUDIT_LOG")

        # Log sensitive endpoint access with enhanced security logging
        sensitive_paths = ["/o/", "/api/user/", "/admin/"]
        if any(request.path.startswith(path) for path in sensitive_paths):
            user = (
                request.user
                if hasattr(request, "user") and request.user.is_authenticated
                else None
            )

            log_security_event_standardized(
                unique_id=unique_id,
                event_type="SENSITIVE_ENDPOINT_ACCESS",
                description=f"Access to sensitive endpoint: {request.method} {request.path}",
                user=user,
                request=request,
                metadata={
                    "method": request.method,
                    "path": request.path,
                    "query_params": dict(request.GET),
                    "content_type": request.content_type,
                    "authenticated": hasattr(request, "user")
                    and request.user.is_authenticated,
                    "endpoint_category": "sensitive",
                },
                level="INFO",
            )
        else:
            # Log regular endpoint access for audit purposes
            log_operation_info(
                unique_id=unique_id,
                operation_type="ENDPOINT_ACCESS",
                message=f"Endpoint access: {request.method} {request.path}",
                metadata={
                    "method": request.method,
                    "path": request.path,
                    "authenticated": hasattr(request, "user")
                    and request.user.is_authenticated,
                    "endpoint_category": "regular",
                },
            )

        return None

    def process_response(self, request, response):
        """
        Log response details for audit purposes
        """
        unique_id = generate_unique_request_id()

        # Log response information for audit trail
        log_response_info(
            unique_id=unique_id,
            response_data={"status_code": response.status_code},
            status_code=response.status_code,
            operation_type="AUDIT_RESPONSE",
        )

        # Log failed authentication attempts with enhanced security logging
        if response.status_code in [401, 403]:
            user = (
                request.user
                if hasattr(request, "user") and request.user.is_authenticated
                else None
            )

            log_security_event_standardized(
                unique_id=unique_id,
                event_type="ACCESS_DENIED",
                description=f"Access denied: {response.status_code} for {request.path}",
                user=user,
                request=request,
                metadata={
                    "status_code": response.status_code,
                    "method": request.method,
                    "path": request.path,
                    "response_type": "access_denied",
                },
                level="WARNING",
            )
        elif response.status_code >= 500:
            # Log server errors for monitoring
            log_operation_info(
                unique_id=unique_id,
                operation_type="SERVER_ERROR",
                message=f"Server error: {response.status_code} for {request.path}",
                metadata={
                    "status_code": response.status_code,
                    "method": request.method,
                    "path": request.path,
                    "response_type": "server_error",
                },
                level="ERROR",
            )
        elif response.status_code >= 400:
            # Log client errors for monitoring
            log_operation_info(
                unique_id=unique_id,
                operation_type="CLIENT_ERROR",
                message=f"Client error: {response.status_code} for {request.path}",
                metadata={
                    "status_code": response.status_code,
                    "method": request.method,
                    "path": request.path,
                    "response_type": "client_error",
                },
                level="WARNING",
            )

        return response
