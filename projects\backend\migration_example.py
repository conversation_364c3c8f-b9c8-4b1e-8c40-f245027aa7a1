#!/usr/bin/env python
"""
Example showing how to migrate from manual unique_id passing to thread-local storage.
This demonstrates the before/after of removing unique_id parameters.
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
django.setup()

from agritram.logger_utils import enhanced_log, LoggingContext, log_operation_info


# ============================================================================
# BEFORE: Manual unique_id passing (OLD WAY - DON'T DO THIS ANYMORE)
# ============================================================================

def old_way_process_order(order_data, unique_id):
    """OLD WAY: Manual unique_id passing - cumbersome and error-prone."""
    enhanced_log(
        "Starting order processing",
        operation_type="ORDER_START",
        unique_id=unique_id  # Manual ID passing
    )
    
    old_way_validate_order(order_data, unique_id)  # Must pass ID
    old_way_calculate_total(order_data, unique_id)  # Must pass ID
    old_way_process_payment(order_data, unique_id)  # Must pass ID


def old_way_validate_order(order_data, unique_id):
    """OLD WAY: Function signature polluted with unique_id parameter."""
    enhanced_log(
        "Validating order data",
        operation_type="ORDER_VALIDATION",
        unique_id=unique_id  # Manual ID passing
    )
    
    old_way_check_inventory(order_data['items'], unique_id)  # Must pass ID


def old_way_check_inventory(items, unique_id):
    """OLD WAY: Even deeply nested functions need unique_id parameter."""
    enhanced_log(
        "Checking inventory availability",
        operation_type="INVENTORY_CHECK",
        unique_id=unique_id  # Manual ID passing
    )


def old_way_calculate_total(order_data, unique_id):
    """OLD WAY: More manual ID passing."""
    enhanced_log(
        "Calculating order total",
        operation_type="TOTAL_CALCULATION",
        unique_id=unique_id
    )


def old_way_process_payment(order_data, unique_id):
    """OLD WAY: Payment processing with manual ID."""
    enhanced_log(
        "Processing payment",
        operation_type="PAYMENT_PROCESSING",
        unique_id=unique_id
    )


# ============================================================================
# AFTER: Thread-local storage (NEW WAY - CLEAN AND SIMPLE)
# ============================================================================

def new_way_process_order(order_data):
    """NEW WAY: Clean function signature, no unique_id parameter needed."""
    enhanced_log(
        "Starting order processing",
        operation_type="ORDER_START"
        # No unique_id needed - uses thread-local automatically
    )
    
    new_way_validate_order(order_data)    # Clean function call
    new_way_calculate_total(order_data)   # Clean function call
    new_way_process_payment(order_data)   # Clean function call


def new_way_validate_order(order_data):
    """NEW WAY: Clean function signature."""
    enhanced_log(
        "Validating order data",
        operation_type="ORDER_VALIDATION"
        # Automatically inherits thread-local unique_id
    )
    
    new_way_check_inventory(order_data['items'])  # Clean function call


def new_way_check_inventory(items):
    """NEW WAY: Even deeply nested functions are clean."""
    enhanced_log(
        "Checking inventory availability",
        operation_type="INVENTORY_CHECK"
        # Automatically inherits thread-local unique_id
    )


def new_way_calculate_total(order_data):
    """NEW WAY: Clean and simple."""
    enhanced_log(
        "Calculating order total",
        operation_type="TOTAL_CALCULATION"
    )


def new_way_process_payment(order_data):
    """NEW WAY: Payment processing without manual ID passing."""
    enhanced_log(
        "Processing payment",
        operation_type="PAYMENT_PROCESSING"
    )


# ============================================================================
# DEMONSTRATION
# ============================================================================

def demonstrate_migration():
    """Demonstrate the difference between old and new approaches."""
    print("🔄 Migration Example: Removing unique_id Parameters")
    print("=" * 60)
    
    # Sample order data
    order_data = {
        'items': ['item1', 'item2'],
        'total': 100.00,
        'customer_id': 12345
    }
    
    print("\n❌ OLD WAY: Manual unique_id passing")
    print("-" * 40)
    from agritram.logger_utils import generate_api_call_uuid
    
    # Old way - must generate and pass unique_id manually
    unique_id = generate_api_call_uuid()
    print(f"Generated unique_id: {unique_id}")
    old_way_process_order(order_data, unique_id)
    
    print("\n✅ NEW WAY: Thread-local storage")
    print("-" * 40)
    
    # New way - use context manager, no manual ID passing
    with LoggingContext(operation="order_processing", customer_id=12345):
        new_way_process_order(order_data)
    
    print("\n🎯 Benefits of Removing unique_id:")
    print("   ✓ Cleaner function signatures")
    print("   ✓ No risk of forgetting to pass unique_id")
    print("   ✓ Easier to maintain and refactor")
    print("   ✓ Automatic correlation across all function calls")
    print("   ✓ Less boilerplate code")


# ============================================================================
# MIGRATION CHECKLIST
# ============================================================================

def print_migration_checklist():
    """Print a checklist for migrating existing code."""
    print("\n📋 MIGRATION CHECKLIST")
    print("=" * 60)
    print("1. ✅ Remove unique_id parameters from function signatures")
    print("2. ✅ Remove unique_id arguments from function calls")
    print("3. ✅ Remove unique_id from enhanced_log() calls")
    print("4. ✅ Add LoggingContext() around operation boundaries")
    print("5. ✅ Test that correlation still works correctly")
    print("\n🔍 WHAT TO LOOK FOR:")
    print("   • Functions with unique_id parameters")
    print("   • Function calls passing unique_id")
    print("   • enhanced_log() calls with unique_id=...")
    print("   • log_operation_info() calls with unique_id as first param")
    print("\n⚠️  BACKWARD COMPATIBILITY:")
    print("   • Old code with unique_id will still work")
    print("   • Can migrate gradually, function by function")
    print("   • No breaking changes to existing functionality")


if __name__ == "__main__":
    demonstrate_migration()
    print_migration_checklist()
    
    print("\n" + "=" * 60)
    print("✅ Migration example completed!")
    print("📁 Check logs to see both approaches produce correlated logs")
