from django.utils import timezone
from django.conf import settings
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from oauth2_provider.models import AccessToken, RefreshToken

from agritram.message_utils import StandardErrorResponse, handle_exception_with_logging
from agritram.exceptions import (
    raise_validation_error,
    raise_not_found_error,
    raise_business_logic_error,
)
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
    log_security_event_standardized,
)


@api_view(["POST"])
@permission_classes([AllowAny])
def token_introspect(request):
    """
    OAuth2 Token Introspection endpoint (RFC 7662) - Simplified version
    """
    try:
        token = request.data.get("token")

        if not token:
            raise_validation_error(
                message="Missing token parameter",
                details="Token parameter is required for introspection",
            )

        # Find token in standard OAuth2 models
        token_obj = None
        token_type = None

        try:
            token_obj = AccessToken.objects.get(token=token)
            token_type = "access_token"
        except AccessToken.DoesNotExist:
            try:
                token_obj = RefreshToken.objects.get(token=token)
                token_type = "refresh_token"
            except RefreshToken.DoesNotExist:
                pass

        if not token_obj:
            return Response({"active": False}, status=status.HTTP_200_OK)

        # Check if token is active (not expired)
        is_active = True
        if hasattr(token_obj, "expires") and token_obj.expires:
            is_active = token_obj.expires > timezone.now()

        if not is_active:
            return Response({"active": False}, status=status.HTTP_200_OK)

        # Build introspection response
        response_data = {
            "active": True,
            "client_id": token_obj.application.client_id,
            "username": token_obj.user.email if token_obj.user else None,
            "token_type": token_type,
        }

        # Add scope if available
        if hasattr(token_obj, "scope") and token_obj.scope:
            response_data["scope"] = token_obj.scope

        # Add expiration for access tokens
        if token_type == "access_token" and hasattr(token_obj, "expires"):
            response_data["exp"] = int(token_obj.expires.timestamp())

        # Add user information if available
        if token_obj.user:
            response_data.update(
                {
                    "sub": str(token_obj.user.id),
                    "email": token_obj.user.email,
                    "name": token_obj.user.name,
                    "role": token_obj.user.role,
                }
            )

        return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "TOKEN_INTROSPECTION_ERROR",
            f"Token introspection error: {str(e)}",
            metadata={
                "token_provided": bool(token),
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return handle_exception_with_logging(e, "token introspection")


@api_view(["GET"])
@permission_classes([AllowAny])
def jwks(request):
    """
    JSON Web Key Set endpoint for JWT token validation (RFC 7517)
    Returns the public keys used to verify JWT tokens
    """
    try:
        from django.conf import settings
        import hashlib

        # Generate a key ID based on the secret key (for identification)
        key_id = hashlib.sha256(settings.SECRET_KEY.encode()).hexdigest()[:16]

        # Check if RSA private key is configured for RS256
        rsa_private_key = getattr(settings, "OAUTH2_PROVIDER", {}).get(
            "OIDC_RSA_PRIVATE_KEY"
        )

        if rsa_private_key:
            # RSA-based JWT (RS256) - expose public key
            try:
                from cryptography.hazmat.primitives.serialization import (
                    load_pem_private_key,
                )
                import base64

                # Load the private key
                private_key = load_pem_private_key(
                    rsa_private_key.encode(),
                    password=None,
                )

                # Get the public key
                public_key = private_key.public_key()
                public_numbers = public_key.public_numbers()

                # Convert to base64url format
                def int_to_base64url(val):
                    byte_length = (val.bit_length() + 7) // 8
                    return (
                        base64.urlsafe_b64encode(val.to_bytes(byte_length, "big"))
                        .decode("ascii")
                        .rstrip("=")
                    )

                jwks = {
                    "keys": [
                        {
                            "kty": "RSA",
                            "kid": key_id,
                            "alg": "RS256",
                            "use": "sig",
                            "n": int_to_base64url(public_numbers.n),
                            "e": int_to_base64url(public_numbers.e),
                        }
                    ]
                }
            except Exception as rsa_error:
                unique_id = generate_unique_request_id()
                log_operation_info(
                    unique_id,
                    "JWKS_RSA_KEY_PROCESSING_FALLBACK",
                    f"Failed to process RSA key: {rsa_error}, falling back to HMAC",
                    metadata={
                        "rsa_error": str(rsa_error),
                        "rsa_error_type": rsa_error.__class__.__name__,
                    },
                    level="WARNING",
                )
                # Fall back to HMAC
                jwks = {
                    "keys": [
                        {
                            "kty": "oct",
                            "kid": key_id,
                            "alg": "HS256",
                            "use": "sig",
                        }
                    ]
                }
        else:
            # HMAC-based JWT (HS256) - don't expose the secret key
            jwks = {
                "keys": [
                    {
                        "kty": "oct",
                        "kid": key_id,
                        "alg": "HS256",
                        "use": "sig",
                    }
                ]
            }

        return Response(jwks, status=status.HTTP_200_OK)

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "JWKS_ENDPOINT_ERROR",
            f"JWKS endpoint error: {str(e)}",
            metadata={
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {"error": "server_error", "error_description": "Failed to generate JWKS"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def register_device(request):
    """
    Register a new device for the user
    """
    try:
        from .authentication import DeviceAuthenticationService

        # This would typically require authentication
        email = request.data.get("email")
        device_id = request.data.get("device_id")

        # Get dynamic device information
        from .utils import get_dynamic_device_info

        device_info = get_dynamic_device_info(request, "device_registration")
        device_name = device_info["device_name"]
        device_type = device_info["device_type"]

        if not all([email, device_id]):
            raise_validation_error(
                message="Email and device_id are required",
                details="Both email and device_id must be provided for device registration",
            )

        from django.contrib.auth import get_user_model

        User = get_user_model()

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise_not_found_error(
                message="User not found",
                details=f"No user found with email {email}",
                resource_type="user",
            )

        device = DeviceAuthenticationService.register_device(
            user, device_id, device_name, device_type, request
        )

        if device:
            return Response(
                {
                    "message": "Device registered successfully",
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "is_trusted": device.is_trusted,
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {
                    "error": "registration_failed",
                    "error_description": "Failed to register device",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "DEVICE_REGISTRATION_VIEW_ERROR",
            f"Device registration error: {str(e)}",
            metadata={
                "request_data": request.data if hasattr(request, "data") else {},
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {"error": "server_error", "error_description": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([AllowAny])
def well_known_openid_configuration(request):
    """
    OpenID Connect Discovery endpoint - Simplified
    """
    try:
        base_url = request.build_absolute_uri("/")[:-1]  # Remove trailing slash

        # Check if RSA key is configured to determine supported algorithms
        rsa_private_key = getattr(settings, "OAUTH2_PROVIDER", {}).get(
            "OIDC_RSA_PRIVATE_KEY"
        )

        config = {
            "issuer": base_url,
            "authorization_endpoint": f"{base_url}/o/authorize/",
            "token_endpoint": f"{base_url}/o/token/",
            "introspection_endpoint": f"{base_url}/o/introspect/",
            "revocation_endpoint": f"{base_url}/o/revoke_token/",
            "jwks_uri": f"{base_url}/o/jwks/",
            "response_types_supported": [
                "code",
                "token",
                "id_token",
                "code token",
                "code id_token",
                "token id_token",
                "code token id_token",
            ],
            "subject_types_supported": ["public"],
            "id_token_signing_alg_values_supported": (
                ["RS256", "HS256"] if rsa_private_key else ["HS256"]
            ),
            "token_endpoint_auth_methods_supported": [
                "client_secret_basic",
                "client_secret_post",
                "client_secret_jwt",
                "private_key_jwt",
            ],
            "scopes_supported": list(settings.OAUTH2_PROVIDER.get("SCOPES", {}).keys()),
            "claims_supported": [
                "sub",
                "iss",
                "aud",
                "exp",
                "iat",
                "email",
                "name",
                "role",
                "scope",
            ],
            "grant_types_supported": [
                "authorization_code",
                "client_credentials",
                "refresh_token",
            ],
            "code_challenge_methods_supported": ["S256", "plain"],
        }

        return Response(config, status=status.HTTP_200_OK)

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "OPENID_CONFIGURATION_ERROR",
            f"OpenID configuration error: {str(e)}",
            metadata={
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {"error": "server_error", "error_description": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def create_oauth2_client(request):
    """
    Create a new OAuth2 client application for the authenticated user
    """
    try:
        from .client_management import oauth2_client_manager

        # Extract client data
        name = request.data.get("name", f"Agritram App - {request.user.name}")
        redirect_uris = request.data.get("redirect_uris", [])
        client_type = request.data.get("client_type", "user")

        # Validate input
        if not name or len(name.strip()) < 3:
            raise_validation_error(
                message="Application name must be at least 3 characters",
                details="Provide a meaningful name for your OAuth2 application",
            )

        if not redirect_uris:
            raise_validation_error(
                message="At least one redirect URI is required",
                details="Redirect URIs are required for OAuth2 authorization flow",
            )

        # Create client
        application = oauth2_client_manager.create_client_for_user(
            user=request.user,
            name=name.strip(),
            redirect_uris=redirect_uris,
            client_type=client_type,
        )

        return Response(
            {
                "client_id": application.client_id,
                "client_secret": application.client_secret,
                "name": application.name,
                "redirect_uris": application.redirect_uris.split(),
                "created": application.created,
                "message": "OAuth2 client created successfully",
            },
            status=status.HTTP_201_CREATED,
        )

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "CREATE_OAUTH2_CLIENT_ERROR",
            f"Failed to create OAuth2 client: {str(e)}",
            metadata={
                "user_id": request.user.id if hasattr(request, "user") else None,
                "request_data": request.data if hasattr(request, "data") else {},
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {
                "error": "server_error",
                "error_description": "Failed to create OAuth2 client",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def list_oauth2_clients(request):
    """
    List all OAuth2 clients for the authenticated user
    """
    try:
        from .client_management import oauth2_client_manager

        clients = oauth2_client_manager.list_user_clients(request.user)

        return Response(
            {"clients": clients, "count": len(clients)}, status=status.HTTP_200_OK
        )

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "LIST_OAUTH2_CLIENTS_ERROR",
            f"Failed to list OAuth2 clients: {str(e)}",
            metadata={
                "user_id": request.user.id if hasattr(request, "user") else None,
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {
                "error": "server_error",
                "error_description": "Failed to retrieve OAuth2 clients",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def rotate_client_secret(request):
    """
    Rotate OAuth2 client secret
    """
    try:
        from .client_management import oauth2_client_manager
        from oauth2_provider.models import Application

        client_id = request.data.get("client_id")
        if not client_id:
            raise_validation_error(
                message="client_id is required",
                details="Client ID must be provided to rotate secret",
            )

        # Get application and verify ownership
        try:
            application = Application.objects.get(
                client_id=client_id, user=request.user
            )
        except Application.DoesNotExist:
            raise_not_found_error(
                message="OAuth2 client not found or access denied",
                details=f"No OAuth2 client found with ID {client_id} for this user",
                resource_type="oauth2_client",
                resource_id=client_id,
            )

        # Rotate secret
        new_secret = oauth2_client_manager.rotate_client_secret(
            application, request.user
        )

        return Response(
            {
                "client_id": client_id,
                "client_secret": new_secret,
                "message": "Client secret rotated successfully",
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "ROTATE_CLIENT_SECRET_ERROR",
            f"Failed to rotate client secret: {str(e)}",
            metadata={
                "user_id": request.user.id if hasattr(request, "user") else None,
                "client_id": client_id,
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {
                "error": "server_error",
                "error_description": "Failed to rotate client secret",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def revoke_oauth2_client(request):
    """
    Revoke OAuth2 client and all associated tokens
    """
    try:
        from .client_management import oauth2_client_manager
        from oauth2_provider.models import Application

        client_id = request.data.get("client_id")
        reason = request.data.get("reason", "User request")

        if not client_id:
            raise_validation_error(
                message="client_id is required",
                details="Client ID must be provided to revoke OAuth2 client",
            )

        # Get application and verify ownership
        try:
            application = Application.objects.get(
                client_id=client_id, user=request.user
            )
        except Application.DoesNotExist:
            raise_not_found_error(
                message="OAuth2 client not found or access denied",
                details=f"No OAuth2 client found with ID {client_id} for this user",
                resource_type="oauth2_client",
                resource_id=client_id,
            )

        # Revoke client
        success = oauth2_client_manager.revoke_client(application, reason, request.user)

        if success:
            return Response(
                {"message": "OAuth2 client revoked successfully"},
                status=status.HTTP_200_OK,
            )
        else:
            raise_business_logic_error(
                message="Failed to revoke OAuth2 client",
                details="Unable to complete the client revocation process",
            )

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "REVOKE_OAUTH2_CLIENT_ERROR",
            f"Failed to revoke OAuth2 client: {str(e)}",
            metadata={
                "user_id": request.user.id if hasattr(request, "user") else None,
                "client_id": client_id,
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {
                "error": "server_error",
                "error_description": "Failed to revoke OAuth2 client",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def revoke_user_tokens(request):
    """
    Revoke all tokens for the authenticated user
    """
    try:
        from .token_management import token_manager

        reason = request.data.get("reason", "User request")

        success = token_manager.revoke_user_tokens(request.user, reason)

        if success:
            return Response(
                {"message": "All tokens revoked successfully", "reason": reason},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "error": "failed_to_revoke",
                    "error_description": "Failed to revoke user tokens",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "REVOKE_USER_TOKENS_ERROR",
            f"Failed to revoke user tokens: {str(e)}",
            metadata={
                "user_id": request.user.id if hasattr(request, "user") else None,
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {"error": "server_error", "error_description": "Failed to revoke tokens"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def token_statistics(request):
    """
    Get token statistics for the authenticated user
    """
    try:
        from .token_management import token_manager
        from oauth2_provider.models import AccessToken, RefreshToken

        # Get user-specific token stats
        user_access_tokens = AccessToken.objects.filter(user=request.user)
        user_refresh_tokens = RefreshToken.objects.filter(user=request.user)

        now = timezone.now()
        active_access_tokens = user_access_tokens.filter(expires__gt=now).count()
        active_refresh_tokens = (
            user_refresh_tokens.count()
        )  # Refresh tokens don't have expires field

        user_stats = {
            "access_tokens": {
                "total": user_access_tokens.count(),
                "active": active_access_tokens,
                "expired": user_access_tokens.count() - active_access_tokens,
            },
            "refresh_tokens": {
                "total": user_refresh_tokens.count(),
                "active": active_refresh_tokens,
            },
            "applications": {"total": request.user.oauth2_provider_application.count()},
        }

        return Response(user_stats, status=status.HTTP_200_OK)

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "GET_TOKEN_STATISTICS_ERROR",
            f"Failed to get token statistics: {str(e)}",
            metadata={
                "user_id": request.user.id if hasattr(request, "user") else None,
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {
                "error": "server_error",
                "error_description": "Failed to retrieve token statistics",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_security_events(request):
    """
    Get security events for the authenticated user
    """
    try:
        from .security_logger import security_logger

        days = int(request.GET.get("days", 30))
        event_types = request.GET.getlist("event_types")

        events = security_logger.get_user_security_events(
            user=request.user,
            event_types=event_types if event_types else None,
            days=days,
        )

        # Serialize events
        events_data = []
        for event in events:
            events_data.append(
                {
                    "id": str(event.id),
                    "event_type": event.event_type,
                    "description": event.description,
                    "ip_address": event.ip_address,
                    "device_id": event.device_id,
                    "created_at": event.created_at,
                    "metadata": event.metadata,
                }
            )

        return Response(
            {"events": events_data, "count": len(events_data), "period_days": days},
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "GET_USER_SECURITY_EVENTS_ERROR",
            f"Failed to get user security events: {str(e)}",
            metadata={
                "user_id": request.user.id if hasattr(request, "user") else None,
                "days": days,
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {
                "error": "server_error",
                "error_description": "Failed to retrieve security events",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def security_summary(request):
    """
    Get security summary for the authenticated user
    """
    try:
        from .security_logger import security_logger

        days = int(request.GET.get("days", 7))

        # Get user-specific summary
        user_events = security_logger.get_user_security_events(
            user=request.user, days=days
        )

        # Calculate user-specific metrics
        summary = {
            "total_events": len(user_events),
            "period_days": days,
            "event_types": {},
            "recent_logins": 0,
            "failed_attempts": 0,
            "device_registrations": 0,
            "high_risk_events": 0,
        }

        high_risk_types = [
            "failed_login",
            "suspicious_activity",
            "device_blocked",
            "invalid_token_usage",
        ]

        for event in user_events:
            event_type = event.event_type
            summary["event_types"][event_type] = (
                summary["event_types"].get(event_type, 0) + 1
            )

            if event_type == "login":
                summary["recent_logins"] += 1
            elif event_type == "failed_login":
                summary["failed_attempts"] += 1
            elif event_type == "device_registered":
                summary["device_registrations"] += 1

            if event_type in high_risk_types:
                summary["high_risk_events"] += 1

        return Response(summary, status=status.HTTP_200_OK)

    except Exception as e:
        unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id,
            "GET_SECURITY_SUMMARY_ERROR",
            f"Failed to get security summary: {str(e)}",
            metadata={
                "days": days,
                "error_message": str(e),
                "error_type": e.__class__.__name__,
            },
            level="ERROR",
        )
        return Response(
            {
                "error": "server_error",
                "error_description": "Failed to retrieve security summary",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
