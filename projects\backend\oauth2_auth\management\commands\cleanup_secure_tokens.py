"""
Management command to clean up expired secure tokens
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from oauth2_auth.secure_token_service import secure_token_service
from oauth2_auth.models import SecureToken
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
)


class Command(BaseCommand):
    help = "Clean up expired secure tokens (activation, password reset, etc.)"

    def add_arguments(self, parser):
        parser.add_argument(
            "--days",
            type=int,
            default=30,
            help="Number of days to keep expired tokens for audit purposes (default: 30)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be deleted without actually deleting",
        )
        parser.add_argument(
            "--token-type",
            type=str,
            choices=[
                "activation",
                "password_reset",
                "email_verification",
                "phone_verification",
            ],
            help="Clean up only specific token type",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Show detailed information about cleanup process",
        )

    def handle(self, *args, **options):
        days_to_keep = options["days"]
        dry_run = options["dry_run"]
        token_type = options.get("token_type")
        verbose = options["verbose"]

        self.stdout.write(f"Starting secure token cleanup...")
        self.stdout.write(f"Days to keep: {days_to_keep}")

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No actual deletions will occur")
            )

        if token_type:
            self.stdout.write(f"Token type filter: {token_type}")

        # Get statistics before cleanup
        if verbose:
            self._show_token_statistics(token_type)

        # Perform cleanup
        try:
            if dry_run:
                results = self._dry_run_cleanup(days_to_keep, token_type)
            else:
                results = secure_token_service.cleanup_expired_tokens(days_to_keep)

            # Display results
            self._display_results(results, dry_run)

            # Show statistics after cleanup if verbose
            if verbose and not dry_run:
                self.stdout.write("\nToken statistics after cleanup:")
                self._show_token_statistics(token_type)

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error during cleanup: {str(e)}"))
            unique_id = generate_unique_request_id()
            log_operation_info(
                unique_id,
                "TOKEN_CLEANUP_ERROR",
                f"Token cleanup error: {str(e)}",
                metadata={
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )

    def _show_token_statistics(self, token_type_filter=None):
        """Show current token statistics"""
        query = SecureToken.objects.all()
        if token_type_filter:
            query = query.filter(token_type=token_type_filter)

        total_tokens = query.count()
        active_tokens = query.filter(status="active").count()
        expired_tokens = query.filter(status="expired").count()
        used_tokens = query.filter(status="used").count()
        invalidated_tokens = query.filter(status="invalidated").count()

        # Tokens by type
        token_types = {}
        for token_type in [
            "activation",
            "password_reset",
            "email_verification",
            "phone_verification",
        ]:
            if not token_type_filter or token_type_filter == token_type:
                count = query.filter(token_type=token_type).count()
                if count > 0:
                    token_types[token_type] = count

        self.stdout.write(f"Total tokens: {total_tokens}")
        self.stdout.write(f"  Active: {active_tokens}")
        self.stdout.write(f"  Expired: {expired_tokens}")
        self.stdout.write(f"  Used: {used_tokens}")
        self.stdout.write(f"  Invalidated: {invalidated_tokens}")

        if token_types:
            self.stdout.write("Tokens by type:")
            for token_type, count in token_types.items():
                self.stdout.write(f"  {token_type}: {count}")

    def _dry_run_cleanup(self, days_to_keep, token_type_filter=None):
        """Simulate cleanup without actually deleting"""
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)

        # Build queries
        expired_query = SecureToken.objects.filter(
            expires_at__lt=timezone.now(), created_at__lt=cutoff_date
        )

        used_query = SecureToken.objects.filter(status="used", used_at__lt=cutoff_date)

        invalidated_query = SecureToken.objects.filter(
            status="invalidated", invalidated_at__lt=cutoff_date
        )

        # Apply token type filter if specified
        if token_type_filter:
            expired_query = expired_query.filter(token_type=token_type_filter)
            used_query = used_query.filter(token_type=token_type_filter)
            invalidated_query = invalidated_query.filter(token_type=token_type_filter)

        # Count tokens that would be deleted
        expired_count = expired_query.count()
        used_count = used_query.count()
        invalidated_count = invalidated_query.count()
        total_count = expired_count + used_count + invalidated_count

        return {
            "expired_tokens_deleted": expired_count,
            "used_tokens_deleted": used_count,
            "invalidated_tokens_deleted": invalidated_count,
            "total_deleted": total_count,
        }

    def _display_results(self, results, dry_run=False):
        """Display cleanup results"""
        if "error" in results:
            self.stdout.write(self.style.ERROR(f"Cleanup failed: {results['error']}"))
            return

        action = "Would delete" if dry_run else "Deleted"

        expired_count = results.get("expired_tokens_deleted", 0)
        used_count = results.get("used_tokens_deleted", 0)
        invalidated_count = results.get("invalidated_tokens_deleted", 0)
        total_count = results.get("total_deleted", 0)

        if total_count == 0:
            self.stdout.write(self.style.SUCCESS("No tokens found for cleanup"))
        else:
            self.stdout.write(f"\n{action} {total_count} tokens:")
            if expired_count > 0:
                self.stdout.write(f"  Expired tokens: {expired_count}")
            if used_count > 0:
                self.stdout.write(f"  Used tokens: {used_count}")
            if invalidated_count > 0:
                self.stdout.write(f"  Invalidated tokens: {invalidated_count}")

            if not dry_run:
                self.stdout.write(
                    self.style.SUCCESS(f"Successfully cleaned up {total_count} tokens")
                )

        # Show recommendations
        if not dry_run and total_count > 0:
            self.stdout.write("\nRecommendations:")
            self.stdout.write(
                "- Consider running this cleanup regularly (daily/weekly)"
            )
            self.stdout.write("- Monitor token usage patterns for security insights")
            self.stdout.write(
                "- Adjust retention period based on compliance requirements"
            )
