"""
Token Invalidation Service

Provides immediate token invalidation capabilities for security incidents,
compromised tokens, and emergency security responses.
"""

from typing import List, Dict, Any, Optional
from datetime import timedelta
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from django.core.cache import cache
from oauth2_provider.models import AccessToken, RefreshToken
from .models import SecureToken, DeviceToken, SecurityEvent
from .utils import log_security_event, get_client_ip
from .config import oauth2_security_config
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
    log_security_event_standardized,
    log_database_operation,
    log_business_event,
    create_logging_context,
)

User = get_user_model()


class TokenInvalidationService:
    """
    Service for immediate token invalidation and security incident response
    """

    # Use centralized configuration
    BLACKLIST_CACHE_PREFIX = oauth2_security_config.BLACKLIST_CACHE_PREFIX
    BLACKLIST_CACHE_TIMEOUT = oauth2_security_config.BLACKLIST_CACHE_TIMEOUT

    # Invalidation reasons - use centralized config
    SECURITY_BREACH = oauth2_security_config.INVALIDATION_REASONS["security_breach"]
    COMPROMISED_DEVICE = oauth2_security_config.INVALIDATION_REASONS[
        "compromised_device"
    ]
    SUSPICIOUS_ACTIVITY = oauth2_security_config.INVALIDATION_REASONS[
        "suspicious_activity"
    ]
    ADMIN_ACTION = oauth2_security_config.INVALIDATION_REASONS["admin_action"]
    USER_REQUEST = oauth2_security_config.INVALIDATION_REASONS["user_request"]
    ACCOUNT_LOCKOUT = oauth2_security_config.INVALIDATION_REASONS["account_lockout"]
    PASSWORD_CHANGE = oauth2_security_config.INVALIDATION_REASONS["password_change"]

    @classmethod
    def invalidate_user_tokens(
        cls, user, reason: str, admin_user=None, request=None
    ) -> Dict[str, Any]:
        """
        Invalidate all tokens for a specific user

        Args:
            user: User instance
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)

        Returns:
            dict: Invalidation results
        """
        unique_id = generate_unique_request_id()

        # Create logging context
        logging_context = create_logging_context(
            unique_id=unique_id,
            request=request,
            user=user,
            operation_name="invalidate_user_tokens",
        )

        log_operation_info(
            unique_id=unique_id,
            operation_type="TOKEN_INVALIDATION_START",
            message=f"Starting token invalidation for user {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "reason": reason,
                "admin_user": admin_user.email if admin_user else None,
                "operation": "invalidate_user_tokens",
            },
        )

        try:
            with transaction.atomic():
                results = {
                    "oauth2_access_tokens": 0,
                    "oauth2_refresh_tokens": 0,
                    "secure_tokens": 0,
                    "device_tokens": 0,
                    "total_invalidated": 0,
                }

                # Invalidate OAuth2 access tokens
                access_tokens = AccessToken.objects.filter(user=user)
                for token in access_tokens:
                    cls._blacklist_token(token.token, reason)
                access_token_count = access_tokens.count()
                access_tokens.delete()
                results["oauth2_access_tokens"] = access_token_count

                # Log database operation
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="DELETE",
                    table_name="oauth2_provider_accesstoken",
                    operation_result="SUCCESS",
                    metadata={
                        "user_id": user.id,
                        "tokens_deleted": access_token_count,
                        "reason": reason,
                    },
                )

                # Invalidate OAuth2 refresh tokens
                refresh_tokens = RefreshToken.objects.filter(user=user)
                for token in refresh_tokens:
                    cls._blacklist_token(token.token, reason)
                refresh_token_count = refresh_tokens.count()
                refresh_tokens.delete()
                results["oauth2_refresh_tokens"] = refresh_token_count

                # Log database operation
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="DELETE",
                    table_name="oauth2_provider_refreshtoken",
                    operation_result="SUCCESS",
                    metadata={
                        "user_id": user.id,
                        "tokens_deleted": refresh_token_count,
                        "reason": reason,
                    },
                )

                # Invalidate secure tokens
                secure_tokens = SecureToken.objects.filter(user=user, status="active")
                secure_token_count = secure_tokens.count()
                for token in secure_tokens:
                    token.invalidate(reason)
                results["secure_tokens"] = secure_token_count

                # Log database operation
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="UPDATE",
                    table_name="oauth2_auth_securetoken",
                    operation_result="SUCCESS",
                    metadata={
                        "user_id": user.id,
                        "tokens_invalidated": secure_token_count,
                        "reason": reason,
                    },
                )

                # Invalidate device tokens
                device_tokens = DeviceToken.objects.filter(user=user, is_active=True)
                device_token_count = device_tokens.count()
                device_tokens.update(is_active=False, deactivated_at=timezone.now())
                results["device_tokens"] = device_token_count

                # Log database operation
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="UPDATE",
                    table_name="oauth2_auth_devicetoken",
                    operation_result="SUCCESS",
                    metadata={
                        "user_id": user.id,
                        "tokens_invalidated": device_token_count,
                        "reason": reason,
                    },
                )

                results["total_invalidated"] = (
                    sum(results.values()) - results["total_invalidated"]
                )

                # Log comprehensive security event
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="ALL_TOKENS_INVALIDATED",
                    description=f"All user tokens invalidated: {reason}",
                    user=user,
                    request=request,
                    metadata={
                        "reason": reason,
                        "admin_user": admin_user.email if admin_user else None,
                        "invalidation_results": results,
                        "operation": "invalidate_user_tokens",
                    },
                    level="WARNING",
                )

                # Log business event
                log_business_event(
                    unique_id=unique_id,
                    event_type="TOKEN_INVALIDATION_COMPLETED",
                    description=f"Token invalidation completed for user {user.email}",
                    entity_type="USER",
                    entity_id=str(user.id),
                    metadata={
                        "reason": reason,
                        "admin_user": admin_user.email if admin_user else None,
                        "invalidation_results": results,
                        "total_tokens_invalidated": results["total_invalidated"],
                    },
                )

                log_operation_info(
                    unique_id=unique_id,
                    operation_type="TOKEN_INVALIDATION_SUCCESS",
                    message=f"Successfully invalidated all tokens for user {user.email}",
                    metadata={
                        "user_id": user.id,
                        "user_email": user.email,
                        "invalidation_results": results,
                        "total_invalidated": results["total_invalidated"],
                    },
                )

                return results

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_INVALIDATION_ERROR",
                message=f"Error invalidating user tokens: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "error": str(e),
                    "reason": reason,
                },
                level="ERROR",
            )
            return {"error": str(e)}

    @classmethod
    def invalidate_specific_token(
        cls,
        token_value: str,
        token_type: str,
        reason: str,
        admin_user=None,
        request=None,
    ) -> bool:
        """
        Invalidate a specific token

        Args:
            token_value: Token value to invalidate
            token_type: Type of token ('access', 'refresh', 'secure', 'device')
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)

        Returns:
            bool: Success status
        """
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id=unique_id,
            operation_type="SPECIFIC_TOKEN_INVALIDATION_START",
            message=f"Starting specific token invalidation: {token_type}",
            metadata={
                "token_type": token_type,
                "reason": reason,
                "admin_user": admin_user.email if admin_user else None,
                "operation": "invalidate_specific_token",
            },
        )

        try:
            success = False
            user = None

            if token_type == "access":
                try:
                    token = AccessToken.objects.get(token=token_value)
                    user = token.user
                    cls._blacklist_token(token_value, reason)
                    token.delete()
                    success = True

                    log_database_operation(
                        unique_id=unique_id,
                        operation_type="DELETE",
                        table_name="oauth2_provider_accesstoken",
                        operation_result="SUCCESS",
                        metadata={
                            "user_id": user.id,
                            "token_type": token_type,
                            "reason": reason,
                        },
                    )
                except AccessToken.DoesNotExist:
                    log_operation_info(
                        unique_id=unique_id,
                        operation_type="TOKEN_NOT_FOUND",
                        message=f"Access token not found for invalidation",
                        metadata={"token_type": token_type},
                        level="WARNING",
                    )

            elif token_type == "refresh":
                try:
                    token = RefreshToken.objects.get(token=token_value)
                    user = token.user
                    cls._blacklist_token(token_value, reason)
                    token.delete()
                    success = True

                    log_database_operation(
                        unique_id=unique_id,
                        operation_type="DELETE",
                        table_name="oauth2_provider_refreshtoken",
                        operation_result="SUCCESS",
                        metadata={
                            "user_id": user.id,
                            "token_type": token_type,
                            "reason": reason,
                        },
                    )
                except RefreshToken.DoesNotExist:
                    log_operation_info(
                        unique_id=unique_id,
                        operation_type="TOKEN_NOT_FOUND",
                        message=f"Refresh token not found for invalidation",
                        metadata={"token_type": token_type},
                        level="WARNING",
                    )

            elif token_type == "secure":
                from .utils import hash_token

                token_hash = hash_token(token_value)
                try:
                    token = SecureToken.objects.get(
                        token_hash=token_hash, status="active"
                    )
                    user = token.user
                    token.invalidate(reason)
                    success = True

                    log_database_operation(
                        unique_id=unique_id,
                        operation_type="UPDATE",
                        table_name="oauth2_auth_securetoken",
                        operation_result="SUCCESS",
                        metadata={
                            "user_id": user.id,
                            "token_type": token_type,
                            "reason": reason,
                        },
                    )
                except SecureToken.DoesNotExist:
                    log_operation_info(
                        unique_id=unique_id,
                        operation_type="TOKEN_NOT_FOUND",
                        message=f"Secure token not found for invalidation",
                        metadata={"token_type": token_type},
                        level="WARNING",
                    )

            if success and user:
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="SPECIFIC_TOKEN_INVALIDATED",
                    description=f"Specific {token_type} token invalidated: {reason}",
                    user=user,
                    request=request,
                    metadata={
                        "token_type": token_type,
                        "reason": reason,
                        "admin_user": admin_user.email if admin_user else None,
                        "operation": "invalidate_specific_token",
                    },
                    level="WARNING",
                )

                log_business_event(
                    unique_id=unique_id,
                    event_type="SPECIFIC_TOKEN_INVALIDATED",
                    description=f"Specific {token_type} token invalidated for user {user.email}",
                    entity_type="TOKEN",
                    entity_id=f"{token_type}_{user.id}",
                    metadata={
                        "token_type": token_type,
                        "reason": reason,
                        "user_id": user.id,
                        "admin_user": admin_user.email if admin_user else None,
                    },
                )

                log_operation_info(
                    unique_id=unique_id,
                    operation_type="SPECIFIC_TOKEN_INVALIDATION_SUCCESS",
                    message=f"Successfully invalidated {token_type} token for user {user.email}",
                    metadata={
                        "token_type": token_type,
                        "user_id": user.id,
                        "user_email": user.email,
                        "reason": reason,
                    },
                )
            else:
                log_operation_info(
                    unique_id=unique_id,
                    operation_type="SPECIFIC_TOKEN_INVALIDATION_FAILED",
                    message=f"Failed to invalidate {token_type} token",
                    metadata={
                        "token_type": token_type,
                        "reason": reason,
                        "success": success,
                    },
                    level="WARNING",
                )

            return success

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="SPECIFIC_TOKEN_INVALIDATION_ERROR",
                message=f"Error invalidating specific token: {str(e)}",
                metadata={"token_type": token_type, "reason": reason, "error": str(e)},
                level="ERROR",
            )
            return False

    @classmethod
    def invalidate_device_tokens(
        cls, device_id: str, reason: str, admin_user=None, request=None
    ) -> int:
        """
        Invalidate all tokens for a specific device

        Args:
            device_id: Device identifier
            reason: Reason for invalidation
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)

        Returns:
            int: Number of tokens invalidated
        """
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id=unique_id,
            operation_type="DEVICE_TOKEN_INVALIDATION_START",
            message=f"Starting device token invalidation for device {device_id}",
            metadata={
                "device_id": device_id,
                "reason": reason,
                "admin_user": admin_user.email if admin_user else None,
                "operation": "invalidate_device_tokens",
            },
        )

        try:
            with transaction.atomic():
                # Invalidate device tokens
                device_tokens = DeviceToken.objects.filter(
                    device_id=device_id, is_active=True
                )
                count = device_tokens.count()

                users_affected = []
                for token in device_tokens:
                    users_affected.append(token.user)

                device_tokens.update(is_active=False, deactivated_at=timezone.now())

                # Log database operation
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="UPDATE",
                    table_name="oauth2_auth_devicetoken",
                    operation_result="SUCCESS",
                    metadata={
                        "device_id": device_id,
                        "tokens_invalidated": count,
                        "reason": reason,
                    },
                )

                # Invalidate OAuth2 tokens for this device (if we can identify them)
                # This is more complex as OAuth2 tokens don't directly link to devices
                # We'll log the event for manual review if needed

                # Log security events for affected users
                for user in set(users_affected):
                    log_security_event(
                        user=user,
                        event_type="device_tokens_invalidated",
                        description=f"Device tokens invalidated: {reason}",
                        ip_address=get_client_ip(request) if request else None,
                        user_agent=(
                            request.META.get("HTTP_USER_AGENT", "") if request else ""
                        ),
                        device_id=device_id,
                        metadata={
                            "reason": reason,
                            "admin_user": admin_user.email if admin_user else None,
                            "tokens_invalidated": count,
                        },
                    )

                    # Also log using standardized security event logging
                    log_security_event_standardized(
                        unique_id=unique_id,
                        event_type="DEVICE_TOKENS_INVALIDATED",
                        description=f"Device tokens invalidated for user {user.email}: {reason}",
                        user=user,
                        request=request,
                        metadata={
                            "device_id": device_id,
                            "reason": reason,
                            "admin_user": admin_user.email if admin_user else None,
                            "tokens_invalidated": count,
                            "operation": "invalidate_device_tokens",
                        },
                        level="WARNING",
                    )

                log_operation_info(
                    unique_id=unique_id,
                    operation_type="DEVICE_TOKEN_INVALIDATION_SUCCESS",
                    message=f"Successfully invalidated {count} device tokens for device {device_id}",
                    metadata={
                        "device_id": device_id,
                        "tokens_invalidated": count,
                        "users_affected": len(set(users_affected)),
                        "reason": reason,
                    },
                )

                return count

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="DEVICE_TOKEN_INVALIDATION_ERROR",
                message=f"Error invalidating device tokens: {str(e)}",
                metadata={
                    "device_id": device_id,
                    "reason": reason,
                    "error": str(e),
                },
                level="ERROR",
            )
            return 0

    @classmethod
    def is_token_blacklisted(cls, token_value: str) -> bool:
        """
        Check if a token is blacklisted

        Args:
            token_value: Token value to check

        Returns:
            bool: True if token is blacklisted
        """
        unique_id = generate_unique_request_id()

        try:
            cache_key = f"{cls.BLACKLIST_CACHE_PREFIX}{hash(token_value)}"
            blacklist_data = cache.get(cache_key)
            is_blacklisted = blacklist_data is not None

            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_BLACKLIST_CHECK",
                message=f"Token blacklist check completed",
                metadata={
                    "is_blacklisted": is_blacklisted,
                    "cache_key_hash": str(hash(cache_key)),
                    "blacklist_data": blacklist_data if is_blacklisted else None,
                },
            )

            return is_blacklisted

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_BLACKLIST_CHECK_ERROR",
                message=f"Error checking token blacklist: {str(e)}",
                metadata={"error": str(e)},
                level="ERROR",
            )
            return False

    @classmethod
    def emergency_invalidate_all(
        cls, reason: str, admin_user, request=None
    ) -> Dict[str, Any]:
        """
        Emergency function to invalidate ALL tokens in the system

        Args:
            reason: Reason for emergency invalidation
            admin_user: Admin user performing the action
            request: HTTP request object (optional)

        Returns:
            dict: Invalidation results
        """
        try:
            with transaction.atomic():
                results = {
                    "oauth2_access_tokens": 0,
                    "oauth2_refresh_tokens": 0,
                    "secure_tokens": 0,
                    "device_tokens": 0,
                    "total_invalidated": 0,
                }

                # Invalidate all OAuth2 access tokens
                access_tokens = AccessToken.objects.all()
                for token in access_tokens:
                    cls._blacklist_token(token.token, reason)
                results["oauth2_access_tokens"] = access_tokens.count()
                access_tokens.delete()

                # Invalidate all OAuth2 refresh tokens
                refresh_tokens = RefreshToken.objects.all()
                for token in refresh_tokens:
                    cls._blacklist_token(token.token, reason)
                results["oauth2_refresh_tokens"] = refresh_tokens.count()
                refresh_tokens.delete()

                # Invalidate all secure tokens
                secure_tokens = SecureToken.objects.filter(status="active")
                for token in secure_tokens:
                    token.invalidate(reason)
                results["secure_tokens"] = secure_tokens.count()

                # Invalidate all device tokens
                device_tokens = DeviceToken.objects.filter(is_active=True)
                results["device_tokens"] = device_tokens.count()
                device_tokens.update(is_active=False, deactivated_at=timezone.now())

                results["total_invalidated"] = (
                    sum(results.values()) - results["total_invalidated"]
                )

                # Log critical security event with comprehensive logging
                unique_id = generate_unique_request_id()

                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="EMERGENCY_ALL_TOKENS_INVALIDATED",
                    description=f"EMERGENCY: All system tokens invalidated: {reason}",
                    request=request,
                    metadata={
                        "reason": reason,
                        "admin_user": admin_user.email,
                        "invalidation_results": results,
                        "severity": "CRITICAL",
                        "operation": "emergency_invalidate_all",
                    },
                    level="ERROR",
                )

                log_business_event(
                    unique_id=unique_id,
                    event_type="EMERGENCY_TOKEN_INVALIDATION",
                    description=f"EMERGENCY: All system tokens invalidated by {admin_user.email}",
                    entity_type="SYSTEM",
                    entity_id="all_tokens",
                    metadata={
                        "reason": reason,
                        "admin_user": admin_user.email,
                        "invalidation_results": results,
                        "total_tokens_invalidated": results["total_invalidated"],
                        "severity": "CRITICAL",
                    },
                    level="ERROR",
                )

                log_operation_info(
                    unique_id=unique_id,
                    operation_type="EMERGENCY_TOKEN_INVALIDATION_SUCCESS",
                    message=f"EMERGENCY: All tokens invalidated by {admin_user.email}",
                    metadata={
                        "admin_user": admin_user.email,
                        "invalidation_results": results,
                        "total_invalidated": results["total_invalidated"],
                        "reason": reason,
                    },
                    level="ERROR",
                )

                return results

        except Exception as e:
            unique_id = generate_unique_request_id()
            log_operation_info(
                unique_id=unique_id,
                operation_type="EMERGENCY_TOKEN_INVALIDATION_ERROR",
                message=f"Error in emergency token invalidation: {str(e)}",
                metadata={
                    "admin_user": admin_user.email,
                    "reason": reason,
                    "error": str(e),
                },
                level="ERROR",
            )
            return {"error": str(e)}

    @classmethod
    def _blacklist_token(cls, token_value: str, reason: str):
        """Add token to blacklist cache"""
        unique_id = generate_unique_request_id()

        try:
            cache_key = f"{cls.BLACKLIST_CACHE_PREFIX}{hash(token_value)}"
            blacklist_data = {
                "reason": reason,
                "blacklisted_at": timezone.now().isoformat(),
                "unique_id": unique_id,
            }

            cache.set(
                cache_key,
                blacklist_data,
                cls.BLACKLIST_CACHE_TIMEOUT,
            )

            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_BLACKLIST_SUCCESS",
                message=f"Token successfully blacklisted",
                metadata={
                    "reason": reason,
                    "cache_key_hash": str(hash(cache_key)),
                    "timeout": cls.BLACKLIST_CACHE_TIMEOUT,
                },
            )

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_BLACKLIST_ERROR",
                message=f"Error blacklisting token: {str(e)}",
                metadata={"reason": reason, "error": str(e)},
                level="ERROR",
            )


# Global service instance
token_invalidation_service = TokenInvalidationService()
