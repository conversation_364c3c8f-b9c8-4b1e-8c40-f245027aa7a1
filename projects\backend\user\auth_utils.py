from django.contrib.auth import get_user_model
from typing import Dict, Any

# Import standardized logging utilities
from agritram.logger_utils import (
    generate_unique_request_id,
    log_security_event_standardized,
    log_operation_info,
    create_logging_context,
)


User = get_user_model()


class UserStatusValidator:
    """
    Utility class for validating user account status during authentication
    """

    # Error codes for different validation failures
    ERROR_CODES = {
        "ACCOUNT_DELETED": "ACCOUNT_DELETED",
        "ACCOUNT_INACTIVE": "ACCOUNT_INACTIVE",
        "ACCOUNT_LOCKED": "ACCOUNT_LOCKED",
        "EMAIL_NOT_VERIFIED": "EMAIL_NOT_VERIFIED",
    }

    @classmethod
    def validate_user_status(
        cls, user, log_violations: bool = False, request=None
    ) -> Dict[str, Any]:
        """
        Validate user account status for authentication

        Args:
            user: User instance to validate
            log_violations: Whether to log validation violations
            request: Django request object (optional, for enhanced logging)

        Returns:
            dict: Validation result with is_valid, error_code, error_message, error_details
        """
        # Generate unique ID for logging correlation
        unique_id = generate_unique_request_id()

        if not user:
            if log_violations:
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="USER_VALIDATION_FAILURE",
                    description="User validation failed - user not found",
                    request=request,
                    metadata={"error_code": "USER_NOT_FOUND"},
                    level="WARNING",
                )
            return {
                "is_valid": False,
                "error_code": "USER_NOT_FOUND",
                "error_message": "User not found",
                "error_details": "The specified user does not exist",
            }

        # Check if user account is deleted
        if user.is_deleted:
            if log_violations:
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="ACCOUNT_VALIDATION_BLOCKED",
                    description="Authentication blocked for deleted user account",
                    user=user,
                    request=request,
                    metadata={
                        "error_code": cls.ERROR_CODES["ACCOUNT_DELETED"],
                        "user_email": user.email,
                        "validation_type": "account_deleted",
                    },
                    level="WARNING",
                )
            return {
                "is_valid": False,
                "error_code": cls.ERROR_CODES["ACCOUNT_DELETED"],
                "error_message": "Account has been deleted",
                "error_details": "This account has been permanently deleted and cannot be used for authentication",
            }

        # Check if user account is active
        if not user.is_active:
            if log_violations:
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="ACCOUNT_VALIDATION_BLOCKED",
                    description="Authentication blocked for inactive user account",
                    user=user,
                    request=request,
                    metadata={
                        "error_code": cls.ERROR_CODES["ACCOUNT_INACTIVE"],
                        "user_email": user.email,
                        "validation_type": "account_inactive",
                    },
                    level="WARNING",
                )
            return {
                "is_valid": False,
                "error_code": cls.ERROR_CODES["ACCOUNT_INACTIVE"],
                "error_message": "Account is inactive",
                "error_details": "This account is inactive and cannot be used for authentication",
            }

        # Check if account is locked (using existing lockout system)
        if hasattr(user, "is_account_locked") and user.is_account_locked():
            if log_violations:
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="ACCOUNT_VALIDATION_BLOCKED",
                    description="Authentication blocked for locked user account",
                    user=user,
                    request=request,
                    metadata={
                        "error_code": cls.ERROR_CODES["ACCOUNT_LOCKED"],
                        "user_email": user.email,
                        "validation_type": "account_locked",
                    },
                    level="WARNING",
                )
            return {
                "is_valid": False,
                "error_code": cls.ERROR_CODES["ACCOUNT_LOCKED"],
                "error_message": "Account is locked",
                "error_details": "This account has been locked due to multiple failed login attempts",
            }

        # All validations passed - log successful validation if requested
        if log_violations:
            log_operation_info(
                unique_id=unique_id,
                operation_type="USER_VALIDATION_SUCCESS",
                message="User account validation passed all checks",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "validation_checks": ["not_deleted", "is_active", "not_locked"],
                },
                level="INFO",
            )

        return {
            "is_valid": True,
            "error_code": None,
            "error_message": None,
            "error_details": None,
        }

    @classmethod
    def get_user_status_summary(cls, user) -> Dict[str, Any]:
        """
        Get a summary of user status for token validation

        Args:
            user: User instance

        Returns:
            dict: User status summary
        """
        if not user:
            return {}

        return {
            "is_active": user.is_active,
            "is_deleted": user.is_deleted,
            "is_mail_verified": user.is_mail_verified,
            "is_locked": hasattr(user, "is_account_locked")
            and user.is_account_locked(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
        }


def log_authentication_attempt(
    user,
    success: bool,
    method: str,
    ip_address: str = None,
    user_agent: str = None,
    error_code: str = None,
    additional_info: Dict = None,
    request=None,
):
    """
    Log authentication attempts using the standardized security logging system

    Args:
        user: User instance (can be None for failed attempts)
        success: Whether the authentication was successful
        method: Authentication method (e.g., 'password', 'oauth2_token', 'jwt_token', 'session', 'token')
        ip_address: Client IP address
        user_agent: Client user agent string
        error_code: Error code for failed attempts
        additional_info: Additional information to log
        request: Django request object (optional, for enhanced logging)
    """
    try:
        # Generate unique ID for logging correlation
        unique_id = generate_unique_request_id()

        # Prepare metadata
        metadata = {
            "success": success,
            "authentication_method": method,
            "user_id": user.id if user else None,
            "user_email": user.email if user else None,
            "user_role": user.role if user else None,
        }

        if error_code:
            metadata["error_code"] = error_code

        if additional_info:
            metadata.update(additional_info)

        if ip_address:
            metadata["client_ip"] = ip_address

        if user_agent:
            metadata["user_agent"] = user_agent

        # Determine event type and description
        if success:
            event_type = "AUTHENTICATION_SUCCESS"
            description = f"Successful authentication via {method}"
            log_level = "INFO"
        else:
            event_type = "AUTHENTICATION_FAILURE"
            description = f"Failed authentication via {method}"
            if error_code:
                description += f" (Error: {error_code})"
            log_level = "WARNING"

        # Log using standardized security event logging
        log_security_event_standardized(
            unique_id=unique_id,
            event_type=event_type,
            description=description,
            user=user,
            request=request,
            metadata=metadata,
            level=log_level,
        )

        # Also maintain backward compatibility with existing security logger if available
        try:
            from oauth2_auth.security_logger import security_logger

            # Log the event using the existing security logger as well
            security_logger.log_event(
                event_type=event_type.lower(),
                description=description,
                user=user,
                ip_address=ip_address,
                user_agent=user_agent,
                metadata=metadata,
            )
        except ImportError:
            # If security_logger is not available, that's fine - we're using the new standardized logging
            pass

    except Exception as e:
        # Don't let logging errors break authentication - use fallback logging
        error_unique_id = generate_unique_request_id()
        log_operation_info(
            unique_id=error_unique_id,
            operation_type="AUTHENTICATION_LOGGING_ERROR",
            message=f"Failed to log authentication attempt: {str(e)}",
            metadata={
                "original_user_email": user.email if user else None,
                "original_method": method,
                "original_success": success,
                "error": str(e),
            },
            level="ERROR",
        )
