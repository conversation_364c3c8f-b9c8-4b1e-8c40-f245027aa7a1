"""
Activation Service

This service handles all activation-related functionality including:
- Activation token generation
- Activation URL construction
- Email sending
"""

from typing import Dict, Any, <PERSON><PERSON>, Optional
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from oauth2_auth.secure_token_service import secure_token_service
from oauth2_auth.email_service import email_service
from oauth2_auth.utils import get_client_ip
from agritram.message_utils import get_frontend_url_by_role
from agritram.logger_utils import (
    log_operation_info,
    generate_unique_request_id,
)
from user.models import User


class ActivationService:
    """Service for handling user activation token generation and email sending"""

    @staticmethod
    def generate_activation_token(user: User, request, device_registered: bool, 
                                unique_id: str) -> Tuple[str, Any]:
        """
        Generate secure activation token for user
        
        Args:
            user: User instance
            request: HTTP request object
            device_registered: Whether device was registered successfully
            unique_id: Unique request ID for correlation
            
        Returns:
            Tuple of (activation_token: str, token_obj: Any)
        """
        client_ip = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")
        
        activation_token, token_obj = secure_token_service.generate_token(
            user=user,
            token_type="activation",
            request=request,
            metadata={
                "registration_ip": client_ip,
                "registration_user_agent": user_agent,
                "device_registered": device_registered,
            },
        )
        
        log_operation_info(
            unique_id,
            "ACTIVATION_TOKEN_GENERATION",
            f"Generated activation token for user {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "token_type": "activation",
                "device_registered": device_registered,
                "client_ip": client_ip,
            },
        )
        
        return activation_token, token_obj

    @staticmethod
    def construct_activation_url(user: User, activation_token: str, unique_id: str) -> str:
        """
        Construct activation URL using role-specific frontend URL
        
        Args:
            user: User instance
            activation_token: Generated activation token
            unique_id: Unique request ID for correlation
            
        Returns:
            str: Complete activation URL
        """
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        frontend_url = get_frontend_url_by_role(user.role)
        activation_url = f"{frontend_url}/activate-account/{uid}/{activation_token}/"
        
        log_operation_info(
            unique_id,
            "ACTIVATION_URL_CONSTRUCTION",
            f"Constructed activation URL for user {user.email}",
            metadata={
                "user_id": user.id,
                "user_email": user.email,
                "user_role": user.role,
                "frontend_url": frontend_url,
                "uid": uid,
            },
        )
        
        return activation_url

    @staticmethod
    def send_activation_email(user: User, activation_url: str, user_agent: str, 
                            unique_id: str) -> Tuple[bool, Optional[str]]:
        """
        Send activation email to user
        
        Args:
            user: User instance
            activation_url: Activation URL
            user_agent: User agent string
            unique_id: Unique request ID for correlation
            
        Returns:
            Tuple of (success: bool, error_message: Optional[str])
        """
        try:
            email_service.send_registration_activation(user, activation_url, user_agent)
            
            log_operation_info(
                unique_id,
                "ACTIVATION_EMAIL_SENT",
                f"Activation email sent successfully to {user.email}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "activation_url": activation_url,
                    "user_agent": user_agent,
                },
            )
            
            return True, None
            
        except Exception as e:
            error_message = str(e)
            log_operation_info(
                unique_id,
                "ACTIVATION_EMAIL_FAILED",
                f"Failed to send activation email to {user.email}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "error": error_message,
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            
            return False, error_message

    @classmethod
    def handle_activation_flow(cls, user: User, request, device_registered: bool, 
                             unique_id: str) -> Dict[str, Any]:
        """
        Handle the complete activation flow
        
        Args:
            user: User instance
            request: HTTP request object
            device_registered: Whether device was registered successfully
            unique_id: Unique request ID for correlation
            
        Returns:
            Dict containing activation flow results
        """
        user_agent = request.META.get("HTTP_USER_AGENT", "")
        
        # Generate activation token
        activation_token, token_obj = cls.generate_activation_token(
            user, request, device_registered, unique_id
        )
        
        # Construct activation URL
        activation_url = cls.construct_activation_url(user, activation_token, unique_id)
        
        # Send activation email
        email_sent, email_error = cls.send_activation_email(
            user, activation_url, user_agent, unique_id
        )
        
        return {
            "activation_token": activation_token,
            "token_obj": token_obj,
            "activation_url": activation_url,
            "email_sent": email_sent,
            "email_error": email_error,
        }
