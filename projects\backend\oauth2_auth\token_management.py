"""
OAuth2 Token Management and Cleanup System (Legacy Compatibility)
Provides backward compatibility for management commands while using new security services
"""

from datetime import timedelta
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from oauth2_provider.models import AccessToken, RefreshToken, Grant, Application
from .email_service import email_service
from .token_invalidation_service import token_invalidation_service
from .config import oauth2_security_config
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
    log_database_operation,
    log_security_event_standardized,
    log_business_event,
    log_performance_metric,
    create_logging_context,
)

User = get_user_model()


class TokenManager:
    """
    Legacy token management system - refactored to use new security services
    """

    def __init__(self):
        # Use centralized configuration
        self.access_token_lifetime = timedelta(
            seconds=oauth2_security_config.ACCESS_TOKEN_LIFETIME
        )
        self.refresh_token_lifetime = timedelta(
            seconds=oauth2_security_config.REFRESH_TOKEN_LIFETIME
        )
        self.cleanup_batch_size = oauth2_security_config.TOKEN_CLEANUP_THRESHOLDS[
            "cleanup_batch_size"
        ]

    def cleanup_expired_tokens(self) -> Dict[str, int]:
        """
        Clean up expired tokens and grants
        """
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id=unique_id,
            operation_type="TOKEN_CLEANUP_START",
            message="Starting token cleanup operation",
            metadata={
                "batch_size": self.cleanup_batch_size,
                "access_token_lifetime_hours": self.access_token_lifetime.total_seconds()
                / 3600,
                "refresh_token_lifetime_hours": self.refresh_token_lifetime.total_seconds()
                / 3600,
            },
        )

        try:
            now = timezone.now()
            results = {
                "access_tokens_deleted": 0,
                "refresh_tokens_deleted": 0,
                "grants_deleted": 0,
                "errors": 0,
            }

            # Clean up expired access tokens
            try:
                expired_access_tokens = AccessToken.objects.filter(expires__lt=now)[
                    : self.cleanup_batch_size
                ]

                access_token_count = expired_access_tokens.count()
                if access_token_count > 0:
                    expired_access_tokens.delete()
                    results["access_tokens_deleted"] = access_token_count

                    log_database_operation(
                        unique_id=unique_id,
                        operation_type="DELETE",
                        table_name="oauth2_provider_accesstoken",
                        operation_result="SUCCESS",
                        metadata={
                            "records_deleted": access_token_count,
                            "cleanup_type": "expired_access_tokens",
                        },
                    )

            except Exception as e:
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="DELETE",
                    table_name="oauth2_provider_accesstoken",
                    operation_result="FAILURE",
                    metadata={"error": str(e), "cleanup_type": "expired_access_tokens"},
                    level="ERROR",
                )
                results["errors"] += 1

            # Clean up expired refresh tokens
            try:
                # Calculate refresh token expiry based on creation time
                refresh_expiry_cutoff = now - self.refresh_token_lifetime
                expired_refresh_tokens = RefreshToken.objects.filter(
                    created__lt=refresh_expiry_cutoff
                )[: self.cleanup_batch_size]

                refresh_token_count = expired_refresh_tokens.count()
                if refresh_token_count > 0:
                    expired_refresh_tokens.delete()
                    results["refresh_tokens_deleted"] = refresh_token_count

                    log_database_operation(
                        unique_id=unique_id,
                        operation_type="DELETE",
                        table_name="oauth2_provider_refreshtoken",
                        operation_result="SUCCESS",
                        metadata={
                            "records_deleted": refresh_token_count,
                            "cleanup_type": "expired_refresh_tokens",
                        },
                    )

            except Exception as e:
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="DELETE",
                    table_name="oauth2_provider_refreshtoken",
                    operation_result="FAILURE",
                    metadata={
                        "error": str(e),
                        "cleanup_type": "expired_refresh_tokens",
                    },
                    level="ERROR",
                )
                results["errors"] += 1

            # Clean up expired grants
            try:
                expired_grants = Grant.objects.filter(expires__lt=now)[
                    : self.cleanup_batch_size
                ]

                grant_count = expired_grants.count()
                if grant_count > 0:
                    expired_grants.delete()
                    results["grants_deleted"] = grant_count

                    log_database_operation(
                        unique_id=unique_id,
                        operation_type="DELETE",
                        table_name="oauth2_provider_grant",
                        operation_result="SUCCESS",
                        metadata={
                            "records_deleted": grant_count,
                            "cleanup_type": "expired_grants",
                        },
                    )

            except Exception as e:
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="DELETE",
                    table_name="oauth2_provider_grant",
                    operation_result="FAILURE",
                    metadata={"error": str(e), "cleanup_type": "expired_grants"},
                    level="ERROR",
                )
                results["errors"] += 1

            # Log cleanup summary
            total_cleaned = (
                results["access_tokens_deleted"]
                + results["refresh_tokens_deleted"]
                + results["grants_deleted"]
            )

            if total_cleaned > 0:
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="TOKEN_CLEANUP",
                    description=f"Token cleanup completed: {total_cleaned} items removed",
                    metadata=results,
                    level="INFO",
                )

            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_CLEANUP_COMPLETE",
                message=f"Token cleanup operation completed successfully. Total cleaned: {total_cleaned}",
                metadata=results,
            )

            return results

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_CLEANUP_FAILURE",
                message=f"Token cleanup operation failed: {str(e)}",
                metadata={"error": str(e)},
                level="ERROR",
            )
            return {"error": str(e)}

    def revoke_user_tokens(self, user, reason: str = "User request") -> bool:
        """
        Revoke all tokens for a specific user - uses new token invalidation service
        """
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id=unique_id,
            operation_type="USER_TOKEN_REVOCATION_START",
            message=f"Starting token revocation for user {user.email}",
            metadata={"user_id": user.id, "user_email": user.email, "reason": reason},
        )

        try:
            # Use the new token invalidation service for enhanced security
            result = token_invalidation_service.invalidate_user_tokens(
                user=user, reason=reason
            )

            if "error" in result:
                log_operation_info(
                    unique_id=unique_id,
                    operation_type="USER_TOKEN_REVOCATION_FAILURE",
                    message=f"Failed to revoke tokens for user {user.email}: {result['error']}",
                    metadata={
                        "user_id": user.id,
                        "user_email": user.email,
                        "reason": reason,
                        "error": result["error"],
                    },
                    level="ERROR",
                )
                return False

            # Send notification email
            email_service.send_security_alert(
                user,
                "All Tokens Revoked",
                f"All your authentication tokens have been revoked. Reason: {reason}",
                {"reason": reason, "total_tokens": result.get("total_invalidated", 0)},
            )

            # Log successful revocation
            log_security_event_standardized(
                unique_id=unique_id,
                event_type="USER_TOKEN_REVOCATION",
                description=f"All tokens revoked for user {user.email}",
                user=user,
                metadata={
                    "reason": reason,
                    "total_tokens_revoked": result.get("total_invalidated", 0),
                },
                level="WARNING",
            )

            log_operation_info(
                unique_id=unique_id,
                operation_type="USER_TOKEN_REVOCATION_SUCCESS",
                message=f"Successfully revoked {result.get('total_invalidated', 0)} tokens for user {user.email}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "reason": reason,
                    "total_tokens_revoked": result.get("total_invalidated", 0),
                },
            )
            return True

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="USER_TOKEN_REVOCATION_FAILURE",
                message=f"Failed to revoke tokens for user {user.email}: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "reason": reason,
                    "error": str(e),
                },
                level="ERROR",
            )
            return False

    def revoke_application_tokens(
        self, application: Application, reason: str = "Application revoked"
    ) -> bool:
        """
        Revoke all tokens for a specific application
        """
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id=unique_id,
            operation_type="APPLICATION_TOKEN_REVOCATION_START",
            message=f"Starting token revocation for application {application.client_id}",
            metadata={
                "application_id": application.id,
                "client_id": application.client_id,
                "application_name": application.name,
                "reason": reason,
            },
        )

        try:
            with transaction.atomic():
                # Count tokens before deletion
                access_count = AccessToken.objects.filter(
                    application=application
                ).count()
                refresh_count = RefreshToken.objects.filter(
                    application=application
                ).count()
                grant_count = Grant.objects.filter(application=application).count()

                # Delete all tokens
                AccessToken.objects.filter(application=application).delete()
                RefreshToken.objects.filter(application=application).delete()
                Grant.objects.filter(application=application).delete()

                # Log database operations
                total_tokens = access_count + refresh_count + grant_count
                log_database_operation(
                    unique_id=unique_id,
                    operation_type="DELETE",
                    table_name="oauth2_provider_tokens",
                    operation_result="SUCCESS",
                    metadata={
                        "application_id": application.id,
                        "client_id": application.client_id,
                        "access_tokens_deleted": access_count,
                        "refresh_tokens_deleted": refresh_count,
                        "grants_deleted": grant_count,
                        "total_tokens_deleted": total_tokens,
                    },
                )

                # Log security event
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="APPLICATION_TOKENS_REVOKED",
                    description=f"All application tokens revoked: {reason}",
                    user=application.user,
                    metadata={
                        "application_id": application.id,
                        "client_id": application.client_id,
                        "application_name": application.name,
                        "reason": reason,
                        "access_tokens_revoked": access_count,
                        "refresh_tokens_revoked": refresh_count,
                        "grants_revoked": grant_count,
                        "total_tokens_revoked": total_tokens,
                    },
                    level="WARNING",
                )

                log_operation_info(
                    unique_id=unique_id,
                    operation_type="APPLICATION_TOKEN_REVOCATION_SUCCESS",
                    message=f"Successfully revoked {total_tokens} tokens for application {application.client_id}",
                    metadata={
                        "application_id": application.id,
                        "client_id": application.client_id,
                        "reason": reason,
                        "total_tokens_revoked": total_tokens,
                    },
                )
                return True

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="APPLICATION_TOKEN_REVOCATION_FAILURE",
                message=f"Failed to revoke tokens for application {application.client_id}: {str(e)}",
                metadata={
                    "application_id": application.id,
                    "client_id": application.client_id,
                    "reason": reason,
                    "error": str(e),
                },
                level="ERROR",
            )
            return False

    def rotate_refresh_token(
        self, refresh_token: RefreshToken
    ) -> Optional[RefreshToken]:
        """
        Rotate a refresh token (create new one, invalidate old one)
        DEPRECATED: Use jwt_rotation_service.refresh_access_token() instead
        """
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id=unique_id,
            operation_type="DEPRECATED_METHOD_WARNING",
            message="TokenManager.rotate_refresh_token() is deprecated. Use jwt_rotation_service.refresh_access_token() instead.",
            metadata={"deprecated_method": "rotate_refresh_token"},
            level="WARNING",
        )

        try:
            with transaction.atomic():
                # Create new refresh token
                new_refresh_token = RefreshToken.objects.create(
                    user=refresh_token.user,
                    application=refresh_token.application,
                    access_token=refresh_token.access_token,
                )

                # Delete old refresh token
                old_token_id = refresh_token.id
                refresh_token.delete()

                # Log rotation using standardized security logging
                log_security_event_standardized(
                    unique_id=unique_id,
                    event_type="REFRESH_TOKEN_ROTATED",
                    description="Refresh token rotated for security (legacy method)",
                    user=new_refresh_token.user,
                    metadata={
                        "old_token_id": old_token_id,
                        "new_token_id": new_refresh_token.id,
                        "application_id": new_refresh_token.application.id,
                        "deprecated_method": True,
                    },
                    level="INFO",
                )

                log_operation_info(
                    unique_id=unique_id,
                    operation_type="REFRESH_TOKEN_ROTATION_SUCCESS",
                    message=f"Rotated refresh token for user {new_refresh_token.user.email}",
                    metadata={
                        "user_id": new_refresh_token.user.id,
                        "user_email": new_refresh_token.user.email,
                        "old_token_id": old_token_id,
                        "new_token_id": new_refresh_token.id,
                    },
                )
                return new_refresh_token

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="REFRESH_TOKEN_ROTATION_FAILURE",
                message=f"Failed to rotate refresh token: {str(e)}",
                metadata={
                    "error": str(e),
                    "user_id": refresh_token.user.id if refresh_token.user else None,
                    "application_id": (
                        refresh_token.application.id
                        if refresh_token.application
                        else None
                    ),
                },
                level="ERROR",
            )
            return None

    def get_token_statistics(self) -> Dict:
        """
        Get comprehensive token statistics
        """
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id=unique_id,
            operation_type="TOKEN_STATISTICS_START",
            message="Starting token statistics collection",
            metadata={},
        )

        try:
            now = timezone.now()

            # Access token stats
            total_access_tokens = AccessToken.objects.count()
            active_access_tokens = AccessToken.objects.filter(expires__gt=now).count()
            expired_access_tokens = total_access_tokens - active_access_tokens

            # Refresh token stats
            total_refresh_tokens = RefreshToken.objects.count()
            refresh_expiry_cutoff = now - self.refresh_token_lifetime
            active_refresh_tokens = RefreshToken.objects.filter(
                created__gt=refresh_expiry_cutoff
            ).count()
            expired_refresh_tokens = total_refresh_tokens - active_refresh_tokens

            # Grant stats
            total_grants = Grant.objects.count()
            active_grants = Grant.objects.filter(expires__gt=now).count()
            expired_grants = total_grants - active_grants

            # Application stats
            total_applications = Application.objects.count()

            # User stats
            users_with_tokens = AccessToken.objects.values("user").distinct().count()

            statistics = {
                "access_tokens": {
                    "total": total_access_tokens,
                    "active": active_access_tokens,
                    "expired": expired_access_tokens,
                },
                "refresh_tokens": {
                    "total": total_refresh_tokens,
                    "active": active_refresh_tokens,
                    "expired": expired_refresh_tokens,
                },
                "grants": {
                    "total": total_grants,
                    "active": active_grants,
                    "expired": expired_grants,
                },
                "applications": {"total": total_applications},
                "users": {"with_active_tokens": users_with_tokens},
                "cleanup_recommendations": {
                    "expired_access_tokens": expired_access_tokens,
                    "expired_refresh_tokens": expired_refresh_tokens,
                    "expired_grants": expired_grants,
                },
            }

            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_STATISTICS_SUCCESS",
                message="Token statistics collected successfully",
                metadata=statistics,
            )

            return statistics

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_STATISTICS_FAILURE",
                message=f"Failed to get token statistics: {str(e)}",
                metadata={"error": str(e)},
                level="ERROR",
            )
            return {"error": str(e)}

    def validate_token_security(self, access_token: AccessToken, request=None) -> Dict:
        """
        Validate token security and detect anomalies
        DEPRECATED: Use jwt_rotation_service.validate_access_token() and device_validation_service instead
        """
        unique_id = generate_unique_request_id()

        log_operation_info(
            unique_id=unique_id,
            operation_type="DEPRECATED_METHOD_WARNING",
            message="TokenManager.validate_token_security() is deprecated. Use jwt_rotation_service.validate_access_token() and device_validation_service instead.",
            metadata={
                "deprecated_method": "validate_token_security",
                "token_id": access_token.id if access_token else None,
                "user_id": (
                    access_token.user.id if access_token and access_token.user else None
                ),
            },
            level="WARNING",
        )

        try:
            # Basic validation for legacy compatibility
            issues = []
            warnings = []

            # Check token age using centralized configuration
            token_age = timezone.now() - access_token.created
            max_token_age_hours = (
                oauth2_security_config.ACCESS_TOKEN_LIFETIME // 3600
            )  # Convert seconds to hours
            if token_age > timedelta(
                hours=max_token_age_hours * 24
            ):  # Allow 24x the normal lifetime before warning
                warnings.append(f"Token is older than {max_token_age_hours * 24} hours")

            validation_result = {
                "valid": len(issues) == 0,
                "issues": issues,
                "warnings": warnings,
                "token_age_hours": token_age.total_seconds() / 3600,
                "deprecated_method": True,
                "recommendation": "Use jwt_rotation_service and device_validation_service for comprehensive security validation",
            }

            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_SECURITY_VALIDATION_SUCCESS",
                message="Token security validation completed (legacy method)",
                metadata={
                    "token_id": access_token.id,
                    "user_id": access_token.user.id if access_token.user else None,
                    "validation_result": validation_result,
                },
            )

            return validation_result

        except Exception as e:
            log_operation_info(
                unique_id=unique_id,
                operation_type="TOKEN_SECURITY_VALIDATION_FAILURE",
                message=f"Token security validation failed: {str(e)}",
                metadata={
                    "error": str(e),
                    "token_id": access_token.id if access_token else None,
                    "user_id": (
                        access_token.user.id
                        if access_token and access_token.user
                        else None
                    ),
                },
                level="ERROR",
            )
            return {"error": str(e)}


# Global token manager instance
token_manager = TokenManager()
